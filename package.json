{"name": "drcode-ui-testing", "version": "0.1.8", "description": "AI UI testing and automation tool", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/airia-in/ui-testing-extension.git"}, "type": "module", "scripts": {"clean:bundle": "rimraf dist && turbo clean:bundle", "clean:node_modules": "pnpx rimraf node_modules && pnpx turbo clean:node_modules", "clean:turbo": "rimraf .turbo && turbo clean:turbo", "clean": "pnpm clean:bundle && pnpm clean:turbo && pnpm clean:node_modules", "clean:install": "pnpm clean:node_modules && pnpm install --frozen-lockfile", "build": "pnpm clean:bundle && turbo ready && turbo build", "build:firefox": "pnpm clean:bundle && turbo ready && cross-env __FIREFOX__=true turbo build", "zip": "pnpm build && pnpm -F zipper zip", "zip:firefox": "pnpm build:firefox && cross-env __FIREFOX__=true pnpm -F zipper zip", "dev": "turbo ready && cross-env __DEV__=true turbo watch dev --concurrency 20", "dev:firefox": "turbo ready && cross-env __DEV__=true __FIREFOX__=true turbo watch dev --concurrency 20", "e2e": "pnpm build && pnpm zip && turbo e2e", "e2e:firefox": "pnpm build:firefox && pnpm zip:firefox && cross-env __FIREFOX__=true turbo e2e", "type-check": "turbo type-check", "lint": "turbo lint --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix": "turbo lint:fix --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "prettier": "turbo prettier --continue -- --cache --cache-location node_modules/.cache/.prettiercache", "prepare": "husky", "update-version": "bash update_version.sh"}, "dependencies": {"eslint-plugin-tailwindcss": "^3.17.4", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@types/chrome": "0.0.326", "@types/node": "^22.5.5", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "esbuild": "^0.25.1", "eslint": "8.57.0", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.9.0", "eslint-plugin-prettier": "5.2.4", "eslint-plugin-react": "7.35.2", "eslint-plugin-react-hooks": "4.6.2", "husky": "^9.1.4", "lint-staged": "^15.2.7", "postcss": "^8.4.47", "prettier": "^3.3.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "tslib": "^2.6.3", "typescript": "5.5.4", "turbo": "^2.5.3", "vite": "6.3.5", "run-script-os": "^1.1.6"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}, "packageManager": "pnpm@9.15.1", "engines": {"node": ">=22.12.0"}, "pnpm": {"overrides": {"cross-spawn": "^7.0.5", "esbuild": "^0.25.1", "nanoid": "3.3.11"}}}