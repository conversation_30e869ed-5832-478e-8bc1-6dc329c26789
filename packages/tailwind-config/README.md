# @extension/tailwind-config

## Overview
This package provides a **shared Tailwind CSS configuration** for the DrCode UI Testing extension. It defines the design system tokens, utility classes, and styling conventions used across all components and pages of the extension.

## Architecture
- **Design System Foundation**: Centralized design tokens and themes
- **Consistent Styling**: Shared configuration across all extension components
- **Custom Utilities**: Extension-specific utility classes
- **Responsive Design**: Mobile-first responsive breakpoints
- **Dark Mode Support**: Built-in dark/light theme switching

## Configuration Structure

### Base Configuration
```javascript
// tailwind.config.ts
import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
    '../pages/*/src/**/*.{js,ts,jsx,tsx}',
    '../packages/ui/src/**/*.{js,ts,jsx,tsx}',
  ],
  darkMode: 'class', // Enable dark mode with class strategy
  theme: {
    extend: {
      // Custom design tokens
    }
  },
  plugins: [
    // Custom plugins
  ]
};

export default config;
```

### Design Tokens

#### Color Palette
```javascript
// Extended color system
colors: {
  // Brand colors
  primary: {
    50: '#eff6ff',
    100: '#dbeafe', 
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Primary brand color
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  
  // Semantic colors
  success: {
    50: '#ecfdf5',
    500: '#10b981',
    900: '#064e3b',
  },
  warning: {
    50: '#fffbeb',
    500: '#f59e0b',
    900: '#78350f',
  },
  error: {
    50: '#fef2f2',
    500: '#ef4444',
    900: '#7f1d1d',
  },
  
  // Extension-specific colors
  extension: {
    background: 'var(--extension-bg)',
    foreground: 'var(--extension-fg)',
    border: 'var(--extension-border)',
    accent: 'var(--extension-accent)',
  }
}
```

#### Typography
```javascript
// Font configuration
fontFamily: {
  sans: ['Inter', 'system-ui', 'sans-serif'],
  mono: ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace'],
  display: ['Inter Display', 'Inter', 'system-ui', 'sans-serif'],
},

fontSize: {
  xs: ['0.75rem', { lineHeight: '1rem' }],
  sm: ['0.875rem', { lineHeight: '1.25rem' }],
  base: ['1rem', { lineHeight: '1.5rem' }],
  lg: ['1.125rem', { lineHeight: '1.75rem' }],
  xl: ['1.25rem', { lineHeight: '1.75rem' }],
  '2xl': ['1.5rem', { lineHeight: '2rem' }],
  '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
  '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
},

fontWeight: {
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
}
```

#### Spacing and Sizing
```javascript
// Spacing system (4px base unit)
spacing: {
  px: '1px',
  0: '0px',
  0.5: '0.125rem', // 2px
  1: '0.25rem',    // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem',     // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem',    // 12px
  3.5: '0.875rem', // 14px
  4: '1rem',       // 16px
  5: '1.25rem',    // 20px
  6: '1.5rem',     // 24px
  7: '1.75rem',    // 28px
  8: '2rem',       // 32px
  9: '2.25rem',    // 36px
  10: '2.5rem',    // 40px
  // ... extends to larger values
},

// Component-specific sizing
extend: {
  height: {
    'extension-popup': '600px',
    'extension-sidebar': '100vh',
    'toolbar': '3rem',
  },
  width: {
    'extension-popup': '400px',
    'extension-sidebar': '20rem',
    'extension-wide': '60rem',
  }
}
```

### Custom Components

#### Extension-Specific Components
```javascript
// Custom component classes
components: {
  '.extension-popup': {
    '@apply w-extension-popup h-extension-popup bg-white dark:bg-gray-900 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700': {},
  },
  
  '.extension-sidebar': {
    '@apply w-extension-sidebar h-extension-sidebar bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700': {},
  },
  
  '.test-card': {
    '@apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm hover:shadow-md transition-shadow': {},
  },
  
  '.status-badge': {
    '@apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': {},
  },
  
  '.code-block': {
    '@apply bg-gray-100 dark:bg-gray-800 rounded-md p-3 font-mono text-sm overflow-x-auto': {},
  }
}
```

#### Form Components
```javascript
// Form styling utilities
'.form-input': {
  '@apply block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500': {},
},

'.form-label': {
  '@apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1': {},
},

'.form-error': {
  '@apply text-sm text-error-600 dark:text-error-400 mt-1': {},
},

'.button-primary': {
  '@apply bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors': {},
},

'.button-secondary': {
  '@apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors': {},
}
```

### Responsive Design

#### Breakpoints
```javascript
screens: {
  sm: '640px',    // Mobile landscape
  md: '768px',    // Tablet
  lg: '1024px',   // Desktop
  xl: '1280px',   // Large desktop
  '2xl': '1536px', // Extra large desktop
  
  // Extension-specific breakpoints
  'popup': '400px',     // Extension popup width
  'sidebar': '320px',   // Sidebar width
  'wide-popup': '600px', // Wide popup mode
}
```

### Dark Mode Support

#### CSS Variables
```css
/* CSS custom properties for theme switching */
:root {
  --extension-bg: theme('colors.white');
  --extension-fg: theme('colors.gray.900');
  --extension-border: theme('colors.gray.200');
  --extension-accent: theme('colors.primary.500');
}

.dark {
  --extension-bg: theme('colors.gray.900');
  --extension-fg: theme('colors.gray.100');
  --extension-border: theme('colors.gray.700');
  --extension-accent: theme('colors.primary.400');
}
```

#### Dark Mode Utilities
```javascript
// Dark mode specific utilities
'.dark-mode-toggle': {
  '@apply p-2 rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors': {},
},

'.theme-aware-shadow': {
  '@apply shadow-lg dark:shadow-gray-900/20': {},
}
```

## Usage Examples

### Component Styling
```tsx
// Using Tailwind classes in React components
function TestCard({ test, status }) {
  return (
    <div className="test-card">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {test.name}
        </h3>
        <span className={cn(
          'status-badge',
          status === 'passed' ? 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400' :
          status === 'failed' ? 'bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-400' :
          'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
        )}>
          {status}
        </span>
      </div>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        {test.description}
      </p>
    </div>
  );
}
```

### Form Styling
```tsx
function TestConfigForm() {
  return (
    <form className="space-y-4">
      <div>
        <label className="form-label">Test Name</label>
        <input 
          type="text" 
          className="form-input"
          placeholder="Enter test name"
        />
      </div>
      
      <div className="flex gap-2">
        <button type="submit" className="button-primary">
          Save Test
        </button>
        <button type="button" className="button-secondary">
          Cancel
        </button>
      </div>
    </form>
  );
}
```

### Responsive Layout
```tsx
function ExtensionLayout() {
  return (
    <div className="extension-popup md:extension-wide">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <aside className="md:col-span-1">
          <nav className="space-y-2">
            {/* Navigation items */}
          </nav>
        </aside>
        <main className="md:col-span-2">
          {/* Main content */}
        </main>
      </div>
    </div>
  );
}
```

## Build Integration

### PostCSS Configuration
```javascript
// postcss.config.js
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    ...(process.env.NODE_ENV === 'production' ? { cssnano: {} } : {})
  }
};
```

### Purging for Production
```javascript
// Optimize CSS bundle size
purge: {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  options: {
    safelist: [
      // Dynamic classes that shouldn't be purged
      'bg-success-500',
      'bg-error-500',
      'bg-warning-500',
    ]
  }
}
```

This Tailwind configuration package ensures consistent, maintainable, and scalable styling across the entire DrCode UI Testing extension while providing excellent developer experience and performance optimization.
