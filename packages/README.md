# Packages

## Overview
This directory contains **internal workspace packages** that provide shared functionality across the DrCode UI Testing extension. These packages follow a modular architecture pattern, enabling code reuse and maintainability across different components of the extension.

## Architecture
- **Monorepo Structure**: Each package is a standalone module
- **Workspace Dependencies**: Internal packages reference each other
- **TypeScript**: Type-safe development across all packages
- **Build System**: Individual build configurations per package
- **Turbo Integration**: Optimized build orchestration

## Package Overview

### `@extension/shared`
**Purpose**: Common utilities and shared functionality
- Shared constants and enums
- Common utility functions
- Type definitions used across components
- Core business logic abstractions

### `@extension/storage`
**Purpose**: Chrome extension storage abstraction layer
- Chrome storage API wrapper
- Type-safe storage operations
- Data persistence management
- Settings and configuration storage

### `@extension/ui`
**Purpose**: Reusable UI components and design system
- React component library
- Consistent styling and theming
- Common UI patterns and layouts
- Accessible and responsive components

### `@extension/dev-utils`
**Purpose**: Development utilities and helpers
- Development-specific tooling
- Debug utilities and logging
- Testing helpers and mocks
- Build-time utilities

### `@extension/hmr`
**Purpose**: Hot Module Replacement for development
- Live reload functionality
- Development experience enhancement
- Fast refresh capabilities
- Module hot swapping

### `@extension/i18n`
**Purpose**: Internationalization support
- Multi-language support
- Locale management
- Translation key handling
- Message formatting

### `@extension/schema-utils`
**Purpose**: Data validation and schema utilities
- JSON schema validation
- Data type checking
- API response validation
- Configuration schema definitions

### `@extension/tailwind-config`
**Purpose**: Shared Tailwind CSS configuration
- Design system tokens
- Consistent styling across components
- Theme configuration
- Utility class definitions

### `@extension/tsconfig`
**Purpose**: Shared TypeScript configurations
- Base TypeScript settings
- App-specific configurations
- Utility-specific configurations
- Consistent compiler options

### `@extension/vite-config`
**Purpose**: Shared Vite build configurations
- Common build settings
- Development server configuration
- Asset handling rules
- Plugin configurations

### `@extension/zipper`
**Purpose**: Extension packaging utilities
- Zip file generation for distribution
- Asset optimization
- Build artifact packaging
- Store submission preparation

## Package Development Patterns

### Package Structure
```
package-name/
├── package.json        # Package configuration
├── index.ts           # Main entry point
├── build.mjs         # Build configuration
├── tsconfig.json     # TypeScript config
├── lib/              # Built output
└── src/              # Source files (if applicable)
```

### Build Process
Each package uses a consistent build pattern:
```bash
pnpm ready    # Prepare build artifacts
pnpm build    # Build the package
pnpm dev      # Development build with watch
```

### Dependency Management
Packages use workspace dependencies:
```json
{
  "dependencies": {
    "@extension/shared": "workspace:*",
    "@extension/storage": "workspace:*"
  }
}
```

## Usage Patterns

### Importing Shared Code
```typescript
// Import from shared package
import { ApiClient, Logger } from '@extension/shared';

// Import from storage package
import { StorageManager } from '@extension/storage';

// Import from UI package
import { Button, Modal } from '@extension/ui';
```

### Type Definitions
```typescript
// Shared types across packages
import type { 
  TestResult, 
  Configuration, 
  ApiResponse 
} from '@extension/shared';
```

### Storage Operations
```typescript
// Type-safe storage operations
import { storage } from '@extension/storage';

const config = await storage.get<Configuration>('config');
await storage.set('config', updatedConfig);
```

## Development Benefits

### Code Reuse
- Eliminate duplicate code across extension components
- Consistent implementations of common functionality
- Shared business logic and utilities

### Type Safety
- Consistent TypeScript configurations
- Shared type definitions
- Compile-time error detection

### Maintainability
- Modular architecture for easier updates
- Isolated testing of individual packages
- Clear separation of concerns

### Build Optimization
- Incremental builds with Turbo
- Parallel package building
- Dependency-aware build ordering
- Caching for faster rebuilds

## Testing Strategy
Each package can be tested independently:
```bash
# Test specific package
cd packages/shared
pnpm test

# Test all packages
pnpm -r test
```

This modular approach ensures the DrCode UI Testing extension remains maintainable, scalable, and developer-friendly while providing a consistent experience across all components.
