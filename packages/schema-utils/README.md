# @extension/schema-utils

## Overview
This package provides **JSON schema validation and data utilities** for the DrCode UI Testing extension. It ensures data integrity, validates API responses, configuration files, and user inputs using robust schema validation patterns.

## Architecture
- **JSON Schema**: Industry-standard schema validation
- **TypeScript Integration**: Generate types from schemas
- **Runtime Validation**: Validate data at runtime
- **Build-Time Validation**: Validate configurations during build

## Key Features

### Schema Definitions
Centralized schema definitions for all data structures:

```typescript
// Test configuration schema
export const testConfigSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1 },
    url: { type: 'string', format: 'uri' },
    timeout: { type: 'number', minimum: 1000, maximum: 300000 },
    retries: { type: 'integer', minimum: 0, maximum: 5 },
    steps: {
      type: 'array',
      items: { $ref: '#/definitions/testStep' }
    }
  },
  required: ['name', 'url', 'steps'],
  additionalProperties: false
};

// API response schema
export const apiResponseSchema = {
  type: 'object',
  properties: {
    success: { type: 'boolean' },
    data: { type: 'object' },
    error: {
      type: 'object',
      properties: {
        code: { type: 'string' },
        message: { type: 'string' }
      }
    }
  },
  required: ['success']
};
```

### Type Generation
Generate TypeScript types from schemas:

```typescript
// Generated types from schemas
export interface TestConfig {
  name: string;
  url: string;
  timeout?: number;
  retries?: number;
  steps: TestStep[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}
```

### Runtime Validation
```typescript
import { validate, validateAsync } from '@extension/schema-utils';

// Synchronous validation
const result = validate(testConfigSchema, userInput);
if (!result.valid) {
  console.error('Validation errors:', result.errors);
  throw new ValidationError(result.errors);
}

// Asynchronous validation with better error handling
try {
  const validData = await validateAsync(testConfigSchema, userInput);
  // Data is guaranteed to match the schema
  processTestConfig(validData);
} catch (error) {
  if (error instanceof ValidationError) {
    showUserFriendlyErrors(error.details);
  }
}
```

## Schema Categories

### Configuration Schemas
**Purpose**: Validate extension settings and configurations

```typescript
// Extension settings schema
export const extensionSettingsSchema = {
  type: 'object',
  properties: {
    theme: { enum: ['light', 'dark', 'auto'] },
    aiProvider: { enum: ['openai', 'anthropic', 'google'] },
    autoSave: { type: 'boolean' },
    debugMode: { type: 'boolean' },
    apiKeys: {
      type: 'object',
      properties: {
        openai: { type: 'string', minLength: 1 },
        anthropic: { type: 'string', minLength: 1 },
        google: { type: 'string', minLength: 1 }
      },
      additionalProperties: false
    }
  },
  required: ['theme', 'aiProvider'],
  additionalProperties: false
};
```

### Test Data Schemas
**Purpose**: Validate test definitions and results

```typescript
// Test step schema
export const testStepSchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    type: { enum: ['click', 'type', 'wait', 'assert'] },
    selector: { type: 'string' },
    value: { type: 'string' },
    timeout: { type: 'number', minimum: 0 },
    optional: { type: 'boolean' }
  },
  required: ['id', 'type'],
  additionalProperties: false
};

// Test result schema
export const testResultSchema = {
  type: 'object',
  properties: {
    testId: { type: 'string' },
    status: { enum: ['passed', 'failed', 'skipped'] },
    duration: { type: 'number', minimum: 0 },
    error: { type: 'string' },
    screenshots: {
      type: 'array',
      items: { type: 'string', format: 'uri' }
    },
    steps: {
      type: 'array',
      items: { $ref: '#/definitions/stepResult' }
    }
  },
  required: ['testId', 'status', 'duration']
};
```

### API Schemas
**Purpose**: Validate API requests and responses

```typescript
// API request schemas
export const apiRequestSchemas = {
  createTest: {
    type: 'object',
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 100 },
      description: { type: 'string', maxLength: 500 },
      config: { $ref: '#/definitions/testConfig' }
    },
    required: ['name', 'config']
  },
  
  updateTest: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string', minLength: 1, maxLength: 100 },
      config: { $ref: '#/definitions/testConfig' }
    },
    required: ['id']
  }
};
```

## Usage Examples

### Form Validation
```typescript
import { createValidator } from '@extension/schema-utils';

const testConfigValidator = createValidator(testConfigSchema);

function TestConfigForm() {
  const [formData, setFormData] = useState<Partial<TestConfig>>({});
  const [errors, setErrors] = useState<ValidationError[]>([]);
  
  const handleSubmit = async (data: unknown) => {
    const validation = testConfigValidator.validate(data);
    
    if (!validation.valid) {
      setErrors(validation.errors);
      return;
    }
    
    // Data is now typed as TestConfig
    await saveTestConfig(validation.data);
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      {errors.map(error => (
        <div key={error.path} className="error">
          {error.message}
        </div>
      ))}
    </form>
  );
}
```

### API Response Validation
```typescript
import { validateApiResponse } from '@extension/schema-utils';

async function fetchTestResults(): Promise<TestResult[]> {
  const response = await fetch('/api/test-results');
  const rawData = await response.json();
  
  // Validate API response structure
  const validatedData = validateApiResponse(rawData, {
    data: {
      type: 'array',
      items: { $ref: '#/definitions/testResult' }
    }
  });
  
  return validatedData.data; // Typed as TestResult[]
}
```

### Configuration Validation
```typescript
import { loadAndValidateConfig } from '@extension/schema-utils';

// Load and validate extension configuration
async function initializeExtension() {
  try {
    const config = await loadAndValidateConfig(
      'extension-config.json',
      extensionSettingsSchema
    );
    
    // Configuration is guaranteed to be valid
    initializeWithConfig(config);
  } catch (error) {
    if (error instanceof ConfigValidationError) {
      // Show user-friendly configuration errors
      showConfigErrorDialog(error.details);
    }
    throw error;
  }
}
```

## Validation Features

### Custom Validators
```typescript
// Custom format validators
import { addFormat } from '@extension/schema-utils';

addFormat('css-selector', {
  validate: (value: string) => {
    try {
      document.querySelector(value);
      return true;
    } catch {
      return false;
    }
  },
  message: 'Must be a valid CSS selector'
});

// Use custom format in schema
const schema = {
  type: 'object',
  properties: {
    selector: { type: 'string', format: 'css-selector' }
  }
};
```

### Conditional Validation
```typescript
// Conditional schema validation
export const conditionalTestSchema = {
  type: 'object',
  properties: {
    type: { enum: ['click', 'type', 'assert'] },
    selector: { type: 'string' },
    value: { type: 'string' }
  },
  required: ['type', 'selector'],
  if: { properties: { type: { const: 'type' } } },
  then: { required: ['value'] }, // 'value' required when type is 'type'
  else: { not: { required: ['value'] } } // 'value' not allowed otherwise
};
```

### Error Formatting
```typescript
import { formatValidationError } from '@extension/schema-utils';

function handleValidationError(error: ValidationError) {
  const userFriendlyMessage = formatValidationError(error, {
    // Custom error messages
    messages: {
      required: (field) => `${field} is required`,
      type: (field, expectedType) => `${field} must be a ${expectedType}`,
      format: (field, format) => `${field} must be a valid ${format}`,
    },
    // Field display names
    fieldNames: {
      'config.timeout': 'Timeout Duration',
      'config.retries': 'Retry Count',
      'steps[0].selector': 'First Step Selector',
    }
  });
  
  showErrorToUser(userFriendlyMessage);
}
```

## Build Integration

### Schema Compilation
```typescript
// Compile schemas at build time for better performance
import { compileSchemas } from '@extension/schema-utils/build';

const compiledSchemas = compileSchemas({
  testConfig: testConfigSchema,
  apiResponse: apiResponseSchema,
  extensionSettings: extensionSettingsSchema,
});

// Use compiled schemas for faster validation
export const validators = {
  testConfig: compiledSchemas.testConfig,
  apiResponse: compiledSchemas.apiResponse,
  extensionSettings: compiledSchemas.extensionSettings,
};
```

### Type Generation
```bash
# Generate TypeScript types from schemas
pnpm generate-types

# Output: types/schemas.d.ts
```

## Testing

### Schema Testing
```typescript
import { testSchema } from '@extension/schema-utils/testing';

describe('Test Configuration Schema', () => {
  testSchema(testConfigSchema, {
    valid: [
      { name: 'Test 1', url: 'https://example.com', steps: [] },
      { name: 'Test 2', url: 'https://test.com', steps: [], timeout: 5000 },
    ],
    invalid: [
      { name: '', url: 'https://example.com', steps: [] }, // Empty name
      { name: 'Test', url: 'invalid-url', steps: [] }, // Invalid URL
      { name: 'Test', url: 'https://example.com' }, // Missing steps
    ]
  });
});
```

This schema utilities package ensures data integrity and type safety throughout the DrCode UI Testing extension, providing robust validation for all data structures and API interactions.
