# @extension/vite-config

## Overview
This package provides **shared Vite build configurations** for the DrCode UI Testing extension. It centralizes build settings, optimizations, and Chrome extension-specific configurations to ensure consistent builds across all packages and applications.

## Architecture
- **Shared Configuration**: Common Vite settings for all extension components
- **Chrome Extension Optimization**: Specialized build settings for extension contexts
- **Development Experience**: Hot reload, fast refresh, and debugging tools
- **Production Optimization**: Bundle splitting, tree shaking, and minification

## Configuration Structure

### Base Configuration
```javascript
// index.mjs
import { defineConfig } from 'vite';
import { resolve } from 'path';

export function createExtensionConfig(options = {}) {
  const {
    entry = 'src/index.ts',
    outDir = 'dist',
    target = 'chrome',
    context = 'page', // 'page', 'content', 'background'
    ...customConfig
  } = options;

  return defineConfig({
    // Base configuration
    root: process.cwd(),
    
    // Build settings
    build: {
      outDir,
      emptyOutDir: true,
      rollupOptions: {
        input: entry,
        output: {
          format: context === 'background' ? 'iife' : 'es',
          entryFileNames: '[name].js',
          chunkFileNames: '[name].js',
          assetFileNames: '[name].[ext]',
        },
      },
      
      // Chrome extension specific
      target: 'es2022',
      minify: process.env.NODE_ENV === 'production',
      sourcemap: process.env.NODE_ENV === 'development',
      
      // Disable code splitting for extension contexts
      rollupOptions: {
        output: {
          manualChunks: undefined,
        },
      },
    },
    
    // Development server
    server: {
      port: 5173,
      strictPort: true,
      hmr: {
        port: 24678,
      },
    },
    
    // Plugin configuration
    plugins: [
      // Will be extended by specific contexts
    ],
    
    // Environment variables
    define: {
      __DEV__: JSON.stringify(process.env.__DEV__ === 'true'),
      __FIREFOX__: JSON.stringify(process.env.__FIREFOX__ === 'true'),
      __OPERA__: JSON.stringify(process.env.__OPERA__ === 'true'),
    },
    
    // Resolve configuration
    resolve: {
      alias: {
        '@': resolve(process.cwd(), 'src'),
        '@extension/shared': resolve(__dirname, '../shared/src'),
        '@extension/storage': resolve(__dirname, '../storage/src'),
        '@extension/ui': resolve(__dirname, '../ui/src'),
      },
    },
    
    ...customConfig,
  });
}
```

## Context-Specific Configurations

### Page Context (Options, Popup, Side Panel)
```javascript
// createPageConfig.js
import { createExtensionConfig } from './index.mjs';
import react from '@vitejs/plugin-react';
import { crx } from '@crxjs/vite-plugin';

export function createPageConfig(options = {}) {
  return createExtensionConfig({
    context: 'page',
    plugins: [
      react({
        // React Fast Refresh
        fastRefresh: process.env.NODE_ENV === 'development',
      }),
      
      // Chrome extension plugin
      crx({ 
        manifest: options.manifest,
        contentScripts: {
          injectCss: true,
        },
      }),
    ],
    
    // Page-specific optimizations
    build: {
      rollupOptions: {
        output: {
          // Allow code splitting for pages
          manualChunks: {
            vendor: ['react', 'react-dom'],
            ui: ['@extension/ui'],
          },
        },
      },
    },
    
    // CSS processing
    css: {
      postcss: {
        plugins: [
          require('tailwindcss'),
          require('autoprefixer'),
        ],
      },
    },
    
    ...options,
  });
}
```

### Content Script Configuration
```javascript
// createContentConfig.js
import { createExtensionConfig } from './index.mjs';

export function createContentConfig(options = {}) {
  return createExtensionConfig({
    context: 'content',
    
    build: {
      rollupOptions: {
        output: {
          // Content scripts need IIFE format
          format: 'iife',
          name: 'ContentScript',
        },
        
        // External dependencies
        external: ['chrome'],
        globals: {
          chrome: 'chrome',
        },
      },
      
      // Content script optimizations
      lib: {
        entry: options.entry || 'src/index.ts',
        formats: ['iife'],
        fileName: 'index',
      },
    },
    
    // Content script specific plugins
    plugins: [
      // CSS injection plugin for content scripts
      {
        name: 'css-injector',
        generateBundle(opts, bundle) {
          // Inject CSS into content script
          for (const file in bundle) {
            if (file.endsWith('.css')) {
              const cssContent = bundle[file].source;
              // Inject CSS code into JS bundle
            }
          }
        },
      },
    ],
    
    ...options,
  });
}
```

### Background Script Configuration
```javascript
// createBackgroundConfig.js
import { createExtensionConfig } from './index.mjs';

export function createBackgroundConfig(options = {}) {
  return createExtensionConfig({
    context: 'background',
    
    build: {
      rollupOptions: {
        output: {
          // Background scripts need IIFE format
          format: 'iife',
          name: 'BackgroundScript',
        },
      },
      
      // Service worker specific settings
      lib: {
        entry: options.entry || 'src/index.ts',
        formats: ['iife'],
        fileName: 'background',
      },
    },
    
    // Background script plugins
    plugins: [
      // Service worker specific optimizations
      {
        name: 'service-worker-optimizer',
        generateBundle(opts, bundle) {
          // Optimize for service worker context
        },
      },
    ],
    
    ...options,
  });
}
```

## Development Features

### Hot Module Replacement
```javascript
// HMR configuration for extension development
export function withHMR(config) {
  if (process.env.NODE_ENV === 'development') {
    config.plugins.push({
      name: 'extension-hmr',
      configureServer(server) {
        server.ws.on('file-changed', ({ file }) => {
          // Reload extension on file changes
          if (file.includes('manifest')) {
            // Full extension reload
            server.ws.send({
              type: 'full-reload',
            });
          } else {
            // Hot module replacement
            server.ws.send({
              type: 'update',
              updates: [{ type: 'js-update', path: file }],
            });
          }
        });
      },
    });
  }
  
  return config;
}
```

### Environment-Specific Builds
```javascript
// Multi-browser support
export function createBrowserConfig(browser = 'chrome') {
  const config = createExtensionConfig();
  
  // Browser-specific optimizations
  switch (browser) {
    case 'firefox':
      config.define.__FIREFOX__ = true;
      config.build.target = 'firefox102';
      break;
      
    case 'opera':
      config.define.__OPERA__ = true;
      config.build.target = 'chrome88';
      break;
      
    default: // chrome
      config.build.target = 'chrome88';
      break;
  }
  
  return config;
}
```

## Production Optimizations

### Bundle Analysis
```javascript
// Bundle analyzer plugin
import { visualizer } from 'rollup-plugin-visualizer';

export function withBundleAnalysis(config) {
  if (process.env.ANALYZE) {
    config.plugins.push(
      visualizer({
        filename: 'dist/bundle-analysis.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
      })
    );
  }
  
  return config;
}
```

### Tree Shaking Configuration
```javascript
// Aggressive tree shaking for production
export function withTreeShaking(config) {
  config.build.rollupOptions.treeshake = {
    preset: 'smallest',
    moduleSideEffects: false,
    propertyReadSideEffects: false,
    unknownGlobalSideEffects: false,
  };
  
  return config;
}
```

### Asset Optimization
```javascript
// Asset optimization plugin
export function withAssetOptimization(config) {
  config.plugins.push({
    name: 'asset-optimizer',
    generateBundle(opts, bundle) {
      // Optimize images, compress files, etc.
      for (const file in bundle) {
        if (file.match(/\.(png|jpg|jpeg|svg)$/)) {
          // Image optimization
        }
      }
    },
  });
  
  return config;
}
```

## Usage Examples

### Page Configuration
```javascript
// pages/options/vite.config.mts
import { defineConfig } from 'vite';
import { createPageConfig } from '@extension/vite-config';
import manifest from './manifest.json';

export default defineConfig(
  createPageConfig({
    entry: 'src/main.tsx',
    outDir: '../../dist/options',
    manifest,
  })
);
```

### Content Script Configuration
```javascript
// pages/content/vite.config.mts
import { defineConfig } from 'vite';
import { createContentConfig } from '@extension/vite-config';

export default defineConfig(
  createContentConfig({
    entry: 'src/index.ts',
    outDir: '../../dist/content',
  })
);
```

### Background Script Configuration
```javascript
// chrome-extension/vite.config.mts
import { defineConfig } from 'vite';
import { createBackgroundConfig } from '@extension/vite-config';

export default defineConfig(
  createBackgroundConfig({
    entry: 'src/background/index.ts',
    outDir: '../dist',
  })
);
```

### Custom Configuration
```javascript
// Custom configuration with additional plugins
import { defineConfig } from 'vite';
import { createExtensionConfig, withHMR, withBundleAnalysis } from '@extension/vite-config';

export default defineConfig(
  withBundleAnalysis(
    withHMR(
      createExtensionConfig({
        // Custom options
        plugins: [
          // Additional plugins
        ],
      })
    )
  )
);
```

## Environment Variables

### Build-Time Variables
```javascript
// Available in all builds
define: {
  __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  __PROD__: JSON.stringify(process.env.NODE_ENV === 'production'),
  __VERSION__: JSON.stringify(process.env.npm_package_version),
  __FIREFOX__: JSON.stringify(process.env.__FIREFOX__ === 'true'),
  __OPERA__: JSON.stringify(process.env.__OPERA__ === 'true'),
  
  // API endpoints
  __API_URL__: JSON.stringify(process.env.API_URL || 'http://localhost:8000'),
  __WS_URL__: JSON.stringify(process.env.WS_URL || 'ws://localhost:8001'),
}
```

### Development Variables
```bash
# .env.development
__DEV__=true
HMR_PORT=24678
VITE_DEV_PORT=5173
API_URL=http://localhost:8000
```

### Production Variables
```bash
# .env.production
NODE_ENV=production
API_URL=https://api.drcode.example.com
ANALYZE=true
```

This Vite configuration package streamlines the build process for all components of the DrCode UI Testing extension, providing optimized builds for different contexts while maintaining excellent development experience.
