# @extension/dev-utils

## Overview
This package provides **development utilities and tooling** specifically designed to enhance the development experience for the DrCode UI Testing extension. It includes debugging helpers, development-only utilities, and tools that streamline the development workflow.

## Architecture
- **Development-Only**: Utilities excluded from production builds
- **Debugging Tools**: Enhanced logging, inspection, and monitoring
- **Build Helpers**: Development build optimization and tooling
- **TypeScript**: Full type safety for development utilities

## Utility Categories

### Debugging Utilities
**Purpose**: Enhanced debugging capabilities during development

```typescript
// Enhanced logging with context
import { logger } from '@extension/dev-utils';

logger.debug('Component rendered', { 
  component: 'TestRunner', 
  props: componentProps 
});

logger.error('API call failed', { 
  endpoint: '/api/test', 
  error: errorDetails 
});

// Performance monitoring
import { performanceMonitor } from '@extension/dev-utils';

const stopTimer = performanceMonitor.start('api-call');
await apiCall();
stopTimer(); // Logs timing information
```

### Development Hooks
**Purpose**: React hooks for development-specific functionality

```typescript
import { 
  useDevPanel, 
  useDebugState, 
  usePerformanceProfiler 
} from '@extension/dev-utils';

function MyComponent() {
  // Development panel for runtime inspection
  const devPanel = useDevPanel();
  
  // Debug state changes
  const [state, setState] = useDebugState(initialState, 'MyComponent');
  
  // Performance profiling
  usePerformanceProfiler('MyComponent');
  
  return <div>...</div>;
}
```

### Mock Utilities
**Purpose**: Mock data and API responses for development

```typescript
import { 
  mockApiClient, 
  generateTestData, 
  createMockExtensionAPI 
} from '@extension/dev-utils';

// Mock API responses
const mockClient = mockApiClient({
  '/api/tests': generateTestData.testResults(10),
  '/api/user': generateTestData.userProfile(),
});

// Mock Chrome extension APIs
const mockChrome = createMockExtensionAPI({
  storage: { local: {}, sync: {} },
  tabs: { active: mockTab },
});
```

### Development Assertions
**Purpose**: Runtime checks and validations for development

```typescript
import { devAssert, devWarning } from '@extension/dev-utils';

function processTestData(data) {
  // Development-only assertions
  devAssert(data.length > 0, 'Test data should not be empty');
  devWarning(data.length < 100, 'Large dataset may impact performance');
  
  return processData(data);
}
```

## Key Features

### Conditional Execution
All utilities are designed to be tree-shaken in production builds:

```typescript
// Only runs in development
if (__DEV__) {
  devUtils.enableDebugMode();
  devUtils.logComponentTree();
}
```

### Chrome DevTools Integration
Enhanced Chrome DevTools experience:

```typescript
// Custom DevTools panels
import { createDevToolsPanel } from '@extension/dev-utils';

createDevToolsPanel('Extension State', {
  getState: () => getCurrentExtensionState(),
  actions: ['clear-storage', 'reset-settings'],
});
```

### Hot Reload Helpers
Support for development hot reloading:

```typescript
// Hot reload utilities
import { hotReload } from '@extension/dev-utils';

hotReload.register('content-script', () => {
  // Reload content script
  window.location.reload();
});

hotReload.register('background', () => {
  // Reload background script
  chrome.runtime.reload();
});
```

## Configuration

### Development Environment Detection
```typescript
// Environment detection
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isExtensionDev = process.env.__DEV__ === 'true';

// Feature flags for development
export const devFeatures = {
  debugPanel: isDevelopment,
  mockAPI: isDevelopment,
  performanceLogging: isDevelopment,
};
```

### Debug Levels
```typescript
// Configurable debug levels
export enum DebugLevel {
  OFF = 0,
  ERROR = 1,
  WARN = 2,
  INFO = 3,
  DEBUG = 4,
  TRACE = 5,
}

// Set debug level
setDebugLevel(DebugLevel.DEBUG);
```

## Usage Examples

### Component Development
```typescript
import React from 'react';
import { 
  withDevTools, 
  useComponentDebugger,
  devLog 
} from '@extension/dev-utils';

function TestComponent({ testId }) {
  // Development debugging
  useComponentDebugger('TestComponent', { testId });
  
  const handleClick = () => {
    devLog('Button clicked', { testId });
    // Handle click logic
  };
  
  return (
    <button onClick={handleClick}>
      Run Test {testId}
    </button>
  );
}

// Wrap with dev tools (no-op in production)
export default withDevTools(TestComponent);
```

### API Development
```typescript
import { mockApiResponse, devTimer } from '@extension/dev-utils';

async function fetchTestResults() {
  const timer = devTimer.start('fetch-test-results');
  
  try {
    // In development, use mock data
    if (__DEV__) {
      return mockApiResponse('/api/test-results', {
        delay: 500, // Simulate network delay
        success: 0.9, // 90% success rate
      });
    }
    
    // Production API call
    const response = await fetch('/api/test-results');
    return response.json();
  } finally {
    timer.stop();
  }
}
```

### State Debugging
```typescript
import { createStateDebugger } from '@extension/dev-utils';

const stateDebugger = createStateDebugger('TestRunner');

function testReducer(state, action) {
  // Log state changes in development
  stateDebugger.logAction(action, state);
  
  switch (action.type) {
    case 'START_TEST':
      return { ...state, running: true };
    default:
      return state;
  }
}
```

## Build Integration

### Webpack/Vite Configuration
```javascript
// Development-only code elimination
export default {
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },
  plugins: [
    // Remove dev-utils in production
    process.env.NODE_ENV === 'production' && {
      name: 'remove-dev-utils',
      transform(code) {
        return code.replace(/from ['"]@extension\/dev-utils['"].*$/gm, '');
      },
    },
  ].filter(Boolean),
};
```

### Tree Shaking
All utilities are designed to be completely removed from production builds:

```typescript
// This entire import and usage will be tree-shaken in production
import { devLog } from '@extension/dev-utils';

if (__DEV__) {
  devLog('This will not appear in production');
}
```

## Testing Integration

### Test Utilities
```typescript
import { 
  createTestRenderer, 
  mockExtensionAPIs, 
  flushPromises 
} from '@extension/dev-utils/testing';

describe('TestComponent', () => {
  beforeEach(() => {
    mockExtensionAPIs();
  });
  
  it('should render correctly', async () => {
    const { render } = createTestRenderer();
    render(<TestComponent />);
    
    await flushPromises();
    // Test assertions
  });
});
```

This package significantly improves the development experience by providing powerful debugging tools, mock utilities, and development helpers while ensuring zero impact on production builds.
