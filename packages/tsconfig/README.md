# @extension/tsconfig

## Overview
This package provides **shared TypeScript configurations** for the DrCode UI Testing extension monorepo. It ensures consistent TypeScript compiler settings, type checking rules, and build configurations across all packages and applications.

## Architecture
- **Base Configuration**: Common TypeScript settings for all packages
- **Specialized Configs**: Specific configurations for different project types
- **Path Mapping**: Workspace-aware module resolution
- **Strict Type Checking**: Enhanced type safety across the codebase

## Configuration Files

### `base.json`
**Purpose**: Fundamental TypeScript settings shared across all packages

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    
    // Strict type checking
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    
    // Enhanced error reporting
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "useDefineForClassFields": true,
    
    // Module resolution
    "baseUrl": ".",
    "paths": {
      "@extension/*": ["../packages/*/src"],
      "@/*": ["./src/*"]
    },
    
    // Type definitions
    "types": ["node", "chrome", "react", "react-dom"]
  },
  "include": [
    "src/**/*",
    "**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    ".turbo"
  ]
}
```

### `app.json`
**Purpose**: Configuration for React applications (pages and UI components)

```json
{
  "extends": "./base.json",
  "compilerOptions": {
    "jsx": "react-jsx",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    
    // Additional DOM types
    "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"],
    
    // React-specific settings
    "types": ["node", "chrome", "react", "react-dom", "react/jsx-runtime"]
  },
  "include": [
    "src/**/*",
    "**/*.d.ts",
    "vite-env.d.ts"
  ]
}
```

### `utils.json`
**Purpose**: Configuration for utility packages and libraries

```json
{
  "extends": "./base.json",
  "compilerOptions": {
    // Library-specific settings
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": false,
    "outDir": "./dist",
    
    // Tree shaking support
    "module": "ESNext",
    "target": "ES2020",
    
    // No DOM types for pure utilities
    "lib": ["ES2022"],
    "types": ["node"]
  },
  "exclude": [
    "node_modules",
    "dist",
    "build",
    ".turbo",
    "**/*.test.ts",
    "**/*.spec.ts"
  ]
}
```

## Path Mapping

### Workspace Path Resolution
```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      // Internal packages
      "@extension/shared": ["../packages/shared/src"],
      "@extension/storage": ["../packages/storage/src"],
      "@extension/ui": ["../packages/ui/src"],
      "@extension/i18n": ["../packages/i18n/src"],
      "@extension/schema-utils": ["../packages/schema-utils/src"],
      
      // Internal aliases
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/types/*": ["./src/types/*"],
      
      // Asset imports
      "@/assets/*": ["./src/assets/*"],
      "@/styles/*": ["./src/styles/*"]
    }
  }
}
```

## Type Safety Features

### Strict Mode Configuration
```json
{
  "compilerOptions": {
    // Core strict checks
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    
    // Additional checks
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    
    // Error prevention
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    "noPropertyAccessFromIndexSignature": true
  }
}
```

### Chrome Extension Types
```json
{
  "compilerOptions": {
    "types": [
      "node",
      "chrome",           // Chrome extension APIs
      "react",
      "react-dom"
    ]
  }
}
```

## Usage Examples

### Package Configuration
```json
// packages/storage/tsconfig.json
{
  "extends": "@extension/tsconfig/utils.json",
  "compilerOptions": {
    "rootDir": "./src",
    "outDir": "./dist"
  },
  "include": [
    "src/**/*"
  ]
}
```

### Application Configuration
```json
// pages/options/tsconfig.json
{
  "extends": "@extension/tsconfig/app.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@extension/*": ["../../packages/*/src"]
    }
  },
  "include": [
    "src/**/*",
    "vite-env.d.ts"
  ]
}
```

### Content Script Configuration
```json
// pages/content/tsconfig.json
{
  "extends": "@extension/tsconfig/app.json",
  "compilerOptions": {
    // Content script specific settings
    "lib": ["ES2022", "DOM"],
    "types": ["chrome"]
  }
}
```

## Type Definitions

### Global Types
```typescript
// types/global.d.ts
declare global {
  // Development flags
  const __DEV__: boolean;
  const __FIREFOX__: boolean;
  const __OPERA__: boolean;
  
  // Extension context
  interface Window {
    chrome?: typeof chrome;
    browser?: typeof chrome; // Firefox compatibility
  }
  
  // Module declarations
  declare module '*.svg' {
    const content: string;
    export default content;
  }
  
  declare module '*.png' {
    const content: string;
    export default content;
  }
  
  declare module '*.css' {
    const content: Record<string, string>;
    export default content;
  }
}

export {};
```

### Chrome Extension Augmentations
```typescript
// types/chrome.d.ts
declare namespace chrome {
  namespace storage {
    interface StorageArea {
      // Enhanced storage methods with type safety
      get<T = any>(keys: string | string[]): Promise<Record<string, T>>;
      set<T = any>(items: Record<string, T>): Promise<void>;
    }
  }
  
  namespace tabs {
    interface Tab {
      // Additional tab properties
      testId?: string;
      isTestTarget?: boolean;
    }
  }
}
```

## Build Integration

### Type Checking Script
```json
// package.json
{
  "scripts": {
    "type-check": "tsc --noEmit",
    "type-check:watch": "tsc --noEmit --watch",
    "type-check:all": "pnpm -r run type-check"
  }
}
```

### IDE Configuration
```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.workspaceSymbols.scope": "allOpenProjects"
}
```

## Compiler Options Explained

### Module Resolution
- **`moduleResolution: "bundler"`**: Uses modern bundler-aware resolution
- **`allowImportingTsExtensions: true`**: Allows importing `.ts` files directly
- **`resolveJsonModule: true`**: Enables JSON imports

### Build Output
- **`noEmit: true`**: TypeScript only for type checking (bundler handles compilation)
- **`isolatedModules: true`**: Ensures each file can be transpiled independently
- **`declaration: true`**: Generates `.d.ts` files for libraries

### Performance
- **`skipLibCheck: true`**: Skips type checking of declaration files for faster compilation
- **`incremental: true`**: Enables incremental compilation
- **`composite: true`**: Enables project references for monorepo builds

This TypeScript configuration package ensures type safety, consistency, and optimal developer experience across the entire DrCode UI Testing extension codebase.
