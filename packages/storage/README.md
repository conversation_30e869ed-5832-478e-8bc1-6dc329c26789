# @extension/storage

## Overview
This package provides a **type-safe abstraction layer** for Chrome extension storage APIs. It manages persistent data storage for the DrCode UI Testing extension, including user settings, chat history, prompts, and profile information.

## Architecture
- **Type-Safe Storage**: Full TypeScript integration with storage operations
- **Chrome Storage API**: Wraps chrome.storage with improved developer experience
- **Structured Data**: Organized storage schemas for different data types
- **Async Operations**: Promise-based API for all storage operations

## Storage Categories

### `/lib/base`
**Purpose**: Core storage infrastructure and base classes
- Base storage client implementation
- Common storage operations (get, set, remove, clear)
- Error handling and retry logic
- Storage event listeners

### `/lib/chat`
**Purpose**: Chat history and conversation storage
- Message persistence
- Conversation threading
- Search and filtering capabilities
- Export/import functionality

### `/lib/profile`
**Purpose**: User profile and account information
- User preferences and settings
- Authentication tokens
- Profile customizations
- Account synchronization

### `/lib/prompt`
**Purpose**: Test prompt and template storage
- Custom test prompts
- Template management
- Prompt history and versioning
- Sharing and collaboration features

### `/lib/settings`
**Purpose**: Extension configuration and preferences
- Application settings
- UI preferences
- Feature toggles
- Debugging options

## Usage Examples

### Basic Storage Operations
```typescript
import { storage } from '@extension/storage';

// Store data
await storage.set('userSettings', {
  theme: 'dark',
  autoSave: true,
  debugMode: false
});

// Retrieve data
const settings = await storage.get<UserSettings>('userSettings');

// Remove data
await storage.remove('userSettings');

// Clear all data
await storage.clear();
```

### Structured Storage Access
```typescript
import { 
  chatStorage, 
  profileStorage, 
  promptStorage, 
  settingsStorage 
} from '@extension/storage';

// Chat operations
await chatStorage.saveMessage(message);
const conversations = await chatStorage.getConversations();

// Profile operations
await profileStorage.updateProfile(profileData);
const user = await profileStorage.getCurrentUser();

// Prompt operations
await promptStorage.savePrompt(prompt);
const templates = await promptStorage.getTemplates();

// Settings operations
await settingsStorage.updateSettings(newSettings);
const config = await settingsStorage.getConfiguration();
```

### Storage Events
```typescript
import { storage } from '@extension/storage';

// Listen for storage changes
storage.onChanged.addListener((changes, areaName) => {
  console.log('Storage changed:', changes);
});

// Listen for specific key changes
storage.watch('userSettings', (newValue, oldValue) => {
  console.log('Settings updated:', { newValue, oldValue });
});
```

## Storage Schema

### Type Definitions
```typescript
interface UserSettings {
  theme: 'light' | 'dark';
  autoSave: boolean;
  debugMode: boolean;
  aiProvider: string;
}

interface ChatMessage {
  id: string;
  content: string;
  timestamp: number;
  role: 'user' | 'assistant';
  conversationId: string;
}

interface TestPrompt {
  id: string;
  name: string;
  template: string;
  variables: Record<string, any>;
  createdAt: number;
  updatedAt: number;
}
```

### Storage Keys
```typescript
// Organized storage key constants
export const STORAGE_KEYS = {
  SETTINGS: 'extension_settings',
  CHAT_HISTORY: 'chat_history',
  USER_PROFILE: 'user_profile',
  PROMPTS: 'test_prompts',
  TEMPLATES: 'prompt_templates'
} as const;
```

## Features

### Data Persistence
- **Local Storage**: Fast access for frequently used data
- **Sync Storage**: Cross-device synchronization for user settings
- **Session Storage**: Temporary data for current session

### Type Safety
```typescript
// Fully typed storage operations
const settings = await storage.get<UserSettings>('settings');
// TypeScript knows settings is of type UserSettings | undefined
```

### Error Handling
```typescript
try {
  await storage.set('key', data);
} catch (error) {
  if (error.code === 'QUOTA_EXCEEDED') {
    // Handle storage quota exceeded
  }
  // Handle other storage errors
}
```

### Data Migration
```typescript
// Handle storage schema migrations
const migrationManager = new StorageMigrationManager();
await migrationManager.migrate(currentVersion, targetVersion);
```

## Development

### Building
```bash
pnpm ready    # Build the storage package
```

### Testing
```bash
pnpm test     # Run storage tests
```

### Type Checking
```bash
pnpm type-check
```

## Storage Limits

### Chrome Storage Limits
- **Local Storage**: ~5MB per extension
- **Sync Storage**: ~100KB per extension, synced across devices
- **Session Storage**: RAM-limited, cleared on browser restart

### Best Practices
- Use appropriate storage area for data type
- Implement data cleanup and rotation
- Handle quota exceeded errors gracefully
- Optimize data structures for storage efficiency

## Testing

### Mock Storage
```typescript
// Test utilities for mocking storage
import { createMockStorage } from '@extension/storage/testing';

const mockStorage = createMockStorage();
// Use mockStorage in tests
```

### Storage Tests
```typescript
describe('Storage Operations', () => {
  it('should store and retrieve data', async () => {
    await storage.set('test', { value: 123 });
    const result = await storage.get('test');
    expect(result).toEqual({ value: 123 });
  });
});
```

This package ensures reliable, type-safe data persistence for all components of the DrCode UI Testing extension while providing a clean, consistent API for storage operations.
