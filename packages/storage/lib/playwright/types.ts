export interface TrackedAction {
  id: string;
  name: string;
  parameters: Record<string, unknown>;
  result: string | null;
  selector?: string;
  xpath?: string;
  timestamp: number;
  sessionId: string;
}

export interface PlaywrightScriptOptions {
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
  includeComments: boolean;
  includeAssertions: boolean;
  includeWaitFor: boolean;
}

export interface SessionInfo {
  sessionId: string | null;
  actionCount: number;
}

export interface PlaywrightExportData {
  actions: TrackedAction[];
  options: PlaywrightScriptOptions;
  sessionInfo: SessionInfo;
  exportedAt: string;
}

export interface GeneratedScript {
  sessionId: string;
  script: string;
  language: PlaywrightScriptOptions['language'];
  generatedAt: number;
  options: PlaywrightScriptOptions;
}
