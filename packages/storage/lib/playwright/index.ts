import { createStorage } from '../base/base';
import { StorageEnum } from '../base/enums';
import type { TrackedAction, PlaywrightScriptOptions } from './types';

// Storage keys
export const PLAYWRIGHT_STORAGE_KEYS = {
  TRACKED_ACTIONS: 'playwright_tracked_actions',
  SCRIPT_OPTIONS: 'playwright_script_options',
  SESSION_INFO: 'playwright_session_info',
  GENERATED_SCRIPTS: 'playwright_generated_scripts',
} as const;

// Default values
const DEFAULT_TRACKED_ACTIONS: TrackedAction[] = [];
const DEFAULT_SCRIPT_OPTIONS: PlaywrightScriptOptions = {
  language: 'javascript',
  includeComments: true,
  includeAssertions: true,
  includeWaitFor: true,
};
const DEFAULT_SESSION_INFO = {
  sessionId: null as string | null,
  actionCount: 0,
};
const DEFAULT_GENERATED_SCRIPTS: Record<string, string> = {};

// Create storage instances
export const trackedActionsStorage = createStorage<TrackedAction[]>(
  PLAYWRIGHT_STORAGE_KEYS.TRACKED_ACTIONS,
  DEFAULT_TRACKED_ACTIONS,
  {
    storageEnum: StorageEnum.Local,
    liveUpdate: true,
  },
);

export const scriptOptionsStorage = createStorage<PlaywrightScriptOptions>(
  PLAYWRIGHT_STORAGE_KEYS.SCRIPT_OPTIONS,
  DEFAULT_SCRIPT_OPTIONS,
  {
    storageEnum: StorageEnum.Local,
    liveUpdate: true,
  },
);

export const sessionInfoStorage = createStorage<{ sessionId: string | null; actionCount: number }>(
  PLAYWRIGHT_STORAGE_KEYS.SESSION_INFO,
  DEFAULT_SESSION_INFO,
  {
    storageEnum: StorageEnum.Local,
    liveUpdate: true,
  },
);

export const generatedScriptsStorage = createStorage<Record<string, string>>(
  PLAYWRIGHT_STORAGE_KEYS.GENERATED_SCRIPTS,
  DEFAULT_GENERATED_SCRIPTS,
  {
    storageEnum: StorageEnum.Local,
    liveUpdate: true,
  },
);

// Helper functions
export const playwrightStorage = {
  // Tracked Actions
  async getTrackedActions(): Promise<TrackedAction[]> {
    return await trackedActionsStorage.get();
  },

  async setTrackedActions(actions: TrackedAction[]): Promise<void> {
    await trackedActionsStorage.set(actions);
  },

  async addTrackedAction(action: TrackedAction): Promise<void> {
    const actions = await trackedActionsStorage.get();
    await trackedActionsStorage.set([...actions, action]);
  },

  async clearTrackedActions(): Promise<void> {
    await trackedActionsStorage.set(DEFAULT_TRACKED_ACTIONS);
  },

  // Script Options
  async getScriptOptions(): Promise<PlaywrightScriptOptions> {
    return await scriptOptionsStorage.get();
  },

  async setScriptOptions(options: PlaywrightScriptOptions): Promise<void> {
    await scriptOptionsStorage.set(options);
  },

  // Session Info
  async getSessionInfo(): Promise<{ sessionId: string | null; actionCount: number }> {
    return await sessionInfoStorage.get();
  },

  async setSessionInfo(info: { sessionId: string | null; actionCount: number }): Promise<void> {
    await sessionInfoStorage.set(info);
  },

  async updateActionCount(count: number): Promise<void> {
    const info = await sessionInfoStorage.get();
    await sessionInfoStorage.set({ ...info, actionCount: count });
  },

  // Generated Scripts
  async getGeneratedScripts(): Promise<Record<string, string>> {
    return await generatedScriptsStorage.get();
  },

  async saveGeneratedScript(sessionId: string, script: string): Promise<void> {
    const scripts = await generatedScriptsStorage.get();
    await generatedScriptsStorage.set({ ...scripts, [sessionId]: script });
  },

  async getGeneratedScript(sessionId: string): Promise<string | undefined> {
    const scripts = await generatedScriptsStorage.get();
    return scripts[sessionId];
  },

  async clearGeneratedScripts(): Promise<void> {
    await generatedScriptsStorage.set(DEFAULT_GENERATED_SCRIPTS);
  },

  // Utility functions
  async exportToJSON(): Promise<string> {
    const actions = await trackedActionsStorage.get();
    const options = await scriptOptionsStorage.get();
    const sessionInfo = await sessionInfoStorage.get();

    return JSON.stringify(
      {
        actions,
        options,
        sessionInfo,
        exportedAt: new Date().toISOString(),
      },
      null,
      2,
    );
  },

  async importFromJSON(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData);

      if (data.actions) {
        await trackedActionsStorage.set(data.actions);
      }

      if (data.options) {
        await scriptOptionsStorage.set(data.options);
      }

      if (data.sessionInfo) {
        await sessionInfoStorage.set(data.sessionInfo);
      }
    } catch (error) {
      throw new Error('Invalid JSON data for import');
    }
  },

  async resetAll(): Promise<void> {
    await Promise.all([
      trackedActionsStorage.set(DEFAULT_TRACKED_ACTIONS),
      scriptOptionsStorage.set(DEFAULT_SCRIPT_OPTIONS),
      sessionInfoStorage.set(DEFAULT_SESSION_INFO),
      generatedScriptsStorage.set(DEFAULT_GENERATED_SCRIPTS),
    ]);
  },
};

export default playwrightStorage;
