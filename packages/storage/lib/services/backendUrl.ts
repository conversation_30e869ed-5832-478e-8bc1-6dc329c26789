import { setDefaultProviders } from '../settings/llmProviders';
import type { GeneralSettingsConfig } from '../settings/generalSettings';
import { generalSettingsStore } from '../settings/generalSettings';

/**
 * Centralized service for managing backend URL configuration
 */
export class BackendUrlService {
  private static instance: BackendUrlService;
  private cachedUrl: string | null = null;

  private constructor() {}

  public static getInstance(): BackendUrlService {
    if (!BackendUrlService.instance) {
      BackendUrlService.instance = new BackendUrlService();
    }
    return BackendUrlService.instance;
  }

  /**
   * Get the current backend URL from settings
   */
  public async getBackendUrl(): Promise<string> {
    try {
      const settings = await generalSettingsStore.getSettings();
      this.cachedUrl = settings.backendUrl;
      return settings.backendUrl;
    } catch (error) {
      console.error('Failed to get backend URL from settings:', error);
      // Fallback to default
      return 'http://localhost:8001';
    }
  }

  /**
   * Get the cached backend URL (synchronous, but may be stale)
   * Use this only when you need synchronous access and have previously called getBackendUrl()
   */
  public getCachedBackendUrl(): string {
    return this.cachedUrl || 'http://localhost:8001';
  }

  /**
   * Update the backend URL in settings
   */
  public async setBackendUrl(url: string): Promise<void> {
    await generalSettingsStore.updateSettings({ backendUrl: url } as Partial<GeneralSettingsConfig>);
    await setDefaultProviders(url);
    this.cachedUrl = url;
  }

  /**
   * Test connection to the backend URL
   */
  public async testConnection(url?: string): Promise<{ success: boolean; error?: string }> {
    const testUrl = url || (await this.getBackendUrl());

    try {
      const response = await fetch(`${testUrl}/api/v1/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout
        signal: AbortSignal.timeout(5000),
      });

      if (response.ok) {
        return { success: true };
      } else {
        return {
          success: false,
          error: `Server responded with status ${response.status}`,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection failed',
      };
    }
  }

  /**
   * Initialize the service by loading the backend URL
   */
  public async initialize(): Promise<void> {
    await this.getBackendUrl();
  }
}

// Export a singleton instance
export const backendUrlService = BackendUrlService.getInstance();

// Convenience function for getting backend URL
export async function getBackendUrl(): Promise<string> {
  return backendUrlService.getBackendUrl();
}

// Convenience function for testing connection
export async function testBackendConnection(url?: string): Promise<{ success: boolean; error?: string }> {
  return backendUrlService.testConnection(url);
}
