# @extension/shared

## Overview
This package provides **shared utilities, hooks, and higher-order components** used throughout the DrCode UI Testing extension. It serves as the foundation for common functionality that needs to be accessible across different parts of the extension.

## Architecture
- **Modular Design**: Organized into logical groups (hooks, HOCs, utils)
- **TypeScript**: Fully typed for safety and developer experience
- **Tree Shakable**: Only import what you need
- **Workspace Package**: Internal package for the monorepo

## Package Contents

### `/lib/hooks`
**Purpose**: Custom React hooks for shared functionality
- State management hooks
- Extension API integration hooks
- Data fetching and caching hooks
- Browser API abstraction hooks

**Common Hooks**:
```typescript
// Example hooks that might be included
useExtensionStorage()  // Chrome storage integration
useTabInfo()          // Current tab information
useContentScript()    // Content script communication
useAsyncState()       // Async operation state management
```

### `/lib/hoc`
**Purpose**: Higher-Order Components for cross-cutting concerns
- Authentication and permissions
- Error boundary wrappers
- Loading state management
- Theme and styling providers

**Common HOCs**:
```typescript
// Example HOCs that might be included
withErrorBoundary()   // Error handling wrapper
withPermissions()     // Permission checking
withLoading()         // Loading state management
withStorage()         // Storage integration
```

### `/lib/utils`
**Purpose**: Utility functions and helper methods
- Data transformation utilities
- Validation functions
- Browser compatibility helpers
- Common calculations and formatters

**Common Utils**:
```typescript
// Example utilities that might be included
formatTestResult()    // Test result formatting
validateInput()       // Data validation
debounce()           // Performance optimization
logger()             // Consistent logging
```

## Usage Examples

### Importing Hooks
```typescript
import { useExtensionStorage, useTabInfo } from '@extension/shared';

function MyComponent() {
  const [settings] = useExtensionStorage('settings');
  const tabInfo = useTabInfo();
  
  return <div>Current tab: {tabInfo.title}</div>;
}
```

### Using HOCs
```typescript
import { withErrorBoundary, withLoading } from '@extension/shared';

const MyComponent = withErrorBoundary(
  withLoading(function BaseComponent({ data }) {
    return <div>{data}</div>;
  })
);
```

### Using Utilities
```typescript
import { formatTestResult, logger } from '@extension/shared';

function processTest(result) {
  const formatted = formatTestResult(result);
  logger.info('Test processed', { result: formatted });
  return formatted;
}
```

## Development

### Building
```bash
pnpm ready    # Build the package
```

### Development
```bash
pnpm dev      # Development build with watch mode
```

### Type Checking
```bash
pnpm type-check
```

## Package Structure
```
shared/
├── package.json      # Package configuration
├── index.ts         # Main exports
├── build.mjs        # Build configuration
├── tsconfig.json    # TypeScript config
└── lib/             # Source code
    ├── hooks/       # Custom React hooks
    ├── hoc/         # Higher-order components
    └── utils/       # Utility functions
```

## Design Principles

### Reusability
All code in this package should be reusable across multiple components and contexts within the extension.

### Type Safety
All exports are fully typed with TypeScript to provide excellent developer experience and catch errors at compile time.

### Performance
Utilities and hooks are optimized for performance, using techniques like memoization and debouncing where appropriate.

### Browser Compatibility
All functionality works consistently across supported browsers (Chrome, Firefox, Opera).

## Dependencies
This package typically depends on:
- `@extension/storage` for storage-related hooks
- React and React DOM for hooks and HOCs
- Chrome extension APIs for browser integration

## Testing
Each utility, hook, and HOC should be thoroughly tested:
```bash
# Run tests for shared package
pnpm test

# Run specific test file
pnpm test hooks/useExtensionStorage.test.ts
```

This package serves as the backbone for shared functionality, ensuring consistency and reducing code duplication across the entire DrCode UI Testing extension.
