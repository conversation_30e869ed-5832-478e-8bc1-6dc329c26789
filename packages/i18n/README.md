# @extension/i18n

## Overview
This package provides **internationalization (i18n) support** for the DrCode UI Testing extension. It manages multi-language support, locale-specific formatting, and translation resources to make the extension accessible to a global audience.

## Architecture
- **Chrome Extension i18n**: Leverages Chrome's built-in internationalization APIs
- **Type-Safe Translations**: TypeScript integration for translation keys
- **Dynamic Locale Loading**: Runtime locale switching
- **Build-Time Generation**: Automated translation file processing

## Key Features

### Multi-Language Support
Currently supports:
- **English (en)**: Primary language
- **Extensible**: Easy addition of new languages

### Translation Management
```typescript
// Type-safe translation keys
import { t } from '@extension/i18n';

// Basic translations
const title = t('extensionName');
const description = t('extensionDescription');

// Parameterized translations
const message = t('testCompleted', { count: 5, duration: '2.3s' });

// Pluralization support
const itemCount = t('itemCount', { count: items.length });
```

### Locale Detection
```typescript
// Automatic locale detection
import { getCurrentLocale, detectBrowserLocale } from '@extension/i18n';

const currentLocale = getCurrentLocale(); // 'en', 'es', 'fr', etc.
const browserLocale = detectBrowserLocale(); // Browser preference

// Fallback chain
const resolvedLocale = resolveLocale([
  userPreference,
  browserLocale,
  'en' // fallback
]);
```

## File Structure

### Locale Files
```
locales/
├── en/
│   ├── messages.json      # Chrome extension messages
│   ├── common.json        # Common UI text
│   ├── testing.json       # Testing-related text
│   └── errors.json        # Error messages
├── es/                    # Spanish translations
│   ├── messages.json
│   └── ...
└── fr/                    # French translations
    ├── messages.json
    └── ...
```

### Message Format (Chrome Extension Standard)
```json
// locales/en/messages.json
{
  "extensionName": {
    "message": "DrCode: UI Testing",
    "description": "Name of the extension"
  },
  "extensionDescription": {
    "message": "AI-powered UI testing and automation tool",
    "description": "Description of the extension"
  },
  "testCompleted": {
    "message": "Test completed: $COUNT$ tests in $DURATION$",
    "description": "Test completion message",
    "placeholders": {
      "count": {
        "content": "$1",
        "example": "5"
      },
      "duration": {
        "content": "$2",
        "example": "2.3s"
      }
    }
  }
}
```

### Structured Translations
```json
// locales/en/testing.json
{
  "actions": {
    "run": "Run Test",
    "stop": "Stop Test",
    "pause": "Pause Test",
    "resume": "Resume Test"
  },
  "status": {
    "running": "Running",
    "passed": "Passed",
    "failed": "Failed",
    "pending": "Pending"
  },
  "results": {
    "summary": "Test Summary",
    "details": "Test Details",
    "duration": "Duration: {{duration}}",
    "assertions": "{{passed}} passed, {{failed}} failed"
  }
}
```

## Usage Examples

### React Components
```typescript
import React from 'react';
import { useTranslation } from '@extension/i18n';

function TestRunner() {
  const { t } = useTranslation('testing');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <button>{t('actions.run')}</button>
      <p>{t('results.duration', { duration: '2.3s' })}</p>
    </div>
  );
}
```

### Background Script
```typescript
// Background script internationalization
import { getMessage } from '@extension/i18n';

chrome.action.setBadgeText({
  text: getMessage('badge.running')
});

chrome.notifications.create({
  type: 'basic',
  iconUrl: 'icon.png',
  title: getMessage('notification.title'),
  message: getMessage('notification.testComplete', {
    count: testResults.length
  })
});
```

### Content Script
```typescript
// Content script with translations
import { t, getCurrentLocale } from '@extension/i18n';

function createTestOverlay() {
  const overlay = document.createElement('div');
  overlay.textContent = t('overlay.instructions');
  
  // Locale-specific styling
  if (getCurrentLocale() === 'ar') {
    overlay.dir = 'rtl'; // Right-to-left languages
  }
  
  document.body.appendChild(overlay);
}
```

## Translation Features

### Parameterization
```typescript
// Named parameters
t('welcome', { username: 'John', count: 5 });
// "Welcome back, John! You have 5 tests."

// Positional parameters (Chrome extension format)
chrome.i18n.getMessage('testResult', ['5', '2']);
// "Completed 5 tests in 2 seconds"
```

### Pluralization
```typescript
// Pluralization rules
const messages = {
  itemCount: {
    0: 'No items',
    1: 'One item',
    other: '{{count}} items'
  }
};

t('itemCount', { count: 0 });  // "No items"
t('itemCount', { count: 1 });  // "One item"
t('itemCount', { count: 5 });  // "5 items"
```

### Date and Number Formatting
```typescript
import { formatDate, formatNumber, formatCurrency } from '@extension/i18n';

// Locale-specific formatting
const date = formatDate(new Date(), 'short');     // US: "1/15/24", EU: "15/1/24"
const number = formatNumber(1234.56);             // US: "1,234.56", EU: "1.234,56"
const duration = formatDuration(125000);          // "2m 5s"
```

## Build Process

### Translation Generation
```javascript
// genenrate-i18n.mjs
import { generateTranslations } from './build-utils.js';

generateTranslations({
  sourceDir: './locales',
  outputDir: './dist/locales',
  defaultLocale: 'en',
  supportedLocales: ['en', 'es', 'fr', 'de', 'ja'],
});
```

### Development Build
```bash
pnpm ready      # Generate and build i18n files for development
```

### Production Build
```bash
pnpm build      # Generate optimized i18n files for production
```

## Advanced Features

### Lazy Loading
```typescript
// Load translations on demand
import { loadLocale } from '@extension/i18n';

async function switchLanguage(locale: string) {
  await loadLocale(locale);
  // Translations are now available
  updateUI();
}
```

### Translation Validation
```typescript
// Validate all translations at build time
import { validateTranslations } from '@extension/i18n/build';

const validationResults = validateTranslations({
  sourceLocale: 'en',
  targetLocales: ['es', 'fr'],
  rules: {
    missingKeys: 'error',
    extraKeys: 'warning',
    emptyValues: 'error',
    parameterMismatch: 'error',
  }
});
```

### Context-Aware Translations
```typescript
// Different translations based on context
const t = useTranslation('testing', {
  context: {
    testType: 'integration',
    userRole: 'admin'
  }
});

// Uses context-specific translations
const message = t('actions.run'); // "Run Integration Test" for admins
```

## Chrome Extension Integration

### Manifest Configuration
```json
{
  "default_locale": "en",
  "name": "__MSG_extensionName__",
  "description": "__MSG_extensionDescription__"
}
```

### Message Passing
```typescript
// Background to content script
chrome.tabs.sendMessage(tabId, {
  type: 'LOCALIZED_MESSAGE',
  key: 'testStarted',
  params: { testName: 'Login Test' }
});

// Content script receives and translates
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'LOCALIZED_MESSAGE') {
    const localizedText = t(message.key, message.params);
    showNotification(localizedText);
  }
});
```

This i18n package ensures the DrCode UI Testing extension can be easily localized for different markets and languages while maintaining type safety and developer productivity.
