# @extension/ui

## Overview
This package provides a **comprehensive UI component library** for the DrCode UI Testing extension. It includes reusable React components, design system tokens, and styling utilities that ensure consistent user experience across all extension interfaces.

## Architecture
- **React Components**: Modern functional components with hooks
- **TypeScript**: Fully typed component props and interfaces
- **Tailwind CSS**: Utility-first styling approach
- **Design System**: Consistent tokens for colors, spacing, typography
- **Accessibility**: WCAG-compliant components with proper ARIA support

## Component Categories

### Form Components
**Purpose**: Input controls and form handling
```typescript
// Example components
<Input 
  placeholder="Enter test name"
  value={value}
  onChange={handleChange}
  error={error}
/>

<Button 
  variant="primary"
  size="medium"
  onClick={handleClick}
  disabled={isLoading}
>
  Run Test
</Button>

<Select
  options={testOptions}
  value={selectedTest}
  onChange={setSelectedTest}
  placeholder="Select a test"
/>
```

### Layout Components
**Purpose**: Page structure and content organization
```typescript
// Example layout components
<Container maxWidth="lg">
  <Header title="Test Dashboard" />
  <Sidebar>
    <Navigation items={navItems} />
  </Sidebar>
  <MainContent>
    <Card>
      <CardHeader>Test Results</CardHeader>
      <CardBody>{content}</CardBody>
    </Card>
  </MainContent>
</Container>
```

### Data Display Components
**Purpose**: Information presentation and visualization
```typescript
// Example data components
<Table
  columns={testColumns}
  data={testResults}
  sortable
  filterable
/>

<Badge variant="success">Passed</Badge>
<Badge variant="error">Failed</Badge>

<ProgressBar 
  value={completionPercentage}
  max={100}
  label="Test Progress"
/>

<Tooltip content="This test checks login functionality">
  <InfoIcon />
</Tooltip>
```

### Feedback Components
**Purpose**: User feedback and notifications
```typescript
// Example feedback components
<Alert 
  type="success"
  title="Test Completed"
  message="All tests passed successfully"
  onClose={handleClose}
/>

<Modal 
  isOpen={showModal}
  onClose={closeModal}
  title="Confirm Delete"
>
  <ModalBody>
    Are you sure you want to delete this test?
  </ModalBody>
  <ModalFooter>
    <Button onClick={closeModal}>Cancel</Button>
    <Button variant="danger" onClick={confirmDelete}>
      Delete
    </Button>
  </ModalFooter>
</Modal>

<LoadingSpinner size="large" />
```

## Design System

### Color Palette
```css
/* Primary colors */
--primary-50: #eff6ff;
--primary-500: #3b82f6;
--primary-900: #1e3a8a;

/* Semantic colors */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #06b6d4;

/* Neutral colors */
--gray-50: #f9fafb;
--gray-500: #6b7280;
--gray-900: #111827;
```

### Typography Scale
```css
/* Font sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */

/* Font weights */
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### Spacing System
```css
/* Spacing scale (based on 4px grid) */
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-4: 1rem;     /* 16px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
```

## Component Props Patterns

### Base Component Props
```typescript
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

interface SizeVariant {
  size?: 'small' | 'medium' | 'large';
}

interface ColorVariant {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}
```

### Interactive Component Props
```typescript
interface InteractiveProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
  onFocus?: (event: React.FocusEvent) => void;
  onBlur?: (event: React.FocusEvent) => void;
}
```

## Usage Examples

### Basic Component Usage
```typescript
import { Button, Input, Card } from '@extension/ui';

function TestForm() {
  const [testName, setTestName] = useState('');
  
  return (
    <Card>
      <Input
        label="Test Name"
        value={testName}
        onChange={(e) => setTestName(e.target.value)}
        placeholder="Enter test name"
      />
      
      <Button 
        type="submit"
        variant="primary"
        disabled={!testName}
      >
        Create Test
      </Button>
    </Card>
  );
}
```

### Styled Components
```typescript
import { cn } from '@extension/ui/utils';

function CustomComponent({ className, ...props }) {
  return (
    <div 
      className={cn(
        'base-styles',
        'hover:bg-gray-100',
        className
      )}
      {...props}
    />
  );
}
```

## Accessibility Features

### Keyboard Navigation
- All interactive components support keyboard navigation
- Focus management for modals and dropdowns
- Proper tab order throughout components

### Screen Reader Support
- Semantic HTML elements
- ARIA labels and descriptions
- Proper heading hierarchy
- Alternative text for images

### Color and Contrast
- WCAG AA compliant color contrasts
- Color is not the only means of conveying information
- Support for high contrast modes

## Theming Support

### Theme Provider
```typescript
import { ThemeProvider } from '@extension/ui';

function App() {
  return (
    <ThemeProvider theme="dark">
      <YourApp />
    </ThemeProvider>
  );
}
```

### Custom Themes
```typescript
const customTheme = {
  colors: {
    primary: '#your-brand-color',
    background: '#your-bg-color',
  },
  fonts: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
  },
};
```

## Development

### Building
```bash
pnpm ready    # Build UI components
```

### Development
```bash
pnpm dev      # Development build with watch
```

### Storybook (if available)
```bash
pnpm storybook  # Component documentation and testing
```

## Testing

### Component Testing
```typescript
import { render, screen } from '@testing-library/react';
import { Button } from '@extension/ui';

test('renders button with correct text', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByRole('button')).toHaveTextContent('Click me');
});
```

### Visual Testing
Components should be tested for:
- Different states (default, hover, active, disabled)
- Different variants and sizes
- Responsive behavior
- Dark/light theme compatibility

This UI package ensures a consistent, accessible, and maintainable user interface across all parts of the DrCode UI Testing extension.
