{"name": "@extension/ui", "version": "0.1.8", "description": "chrome extension - ui components", "private": true, "sideEffects": false, "type": "module", "files": ["dist/**", "dist/global.css"], "types": "index.ts", "main": "./dist/index.js", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "node build.mjs", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "deepmerge": "^4.3.1", "tsc-alias": "^1.8.10"}, "dependencies": {"clsx": "^2.1.1", "tailwind-merge": "^2.4.0"}}