# @extension/zipper

## Overview
This package provides **automated extension packaging and distribution utilities** for the DrCode UI Testing extension. It handles creating production-ready ZIP files for Chrome Web Store, Firefox Add-ons, and other browser extension marketplaces.

## Architecture
- **Multi-Browser Support**: Creates browser-specific packages
- **Asset Optimization**: Compresses and optimizes extension files
- **Manifest Processing**: Validates and transforms manifests for different stores
- **Automated Packaging**: Streamlined build-to-package workflow

## Key Features

### Browser-Specific Packaging
```typescript
// Package for different browsers
import { createPackage } from '@extension/zipper';

// Chrome Web Store package
await createPackage({
  browser: 'chrome',
  inputDir: 'dist',
  outputDir: 'packages',
  version: '1.0.0',
});

// Firefox Add-ons package
await createPackage({
  browser: 'firefox',
  inputDir: 'dist-firefox',
  outputDir: 'packages',
  version: '1.0.0',
});

// Opera Add-ons package
await createPackage({
  browser: 'opera',
  inputDir: 'dist-opera',
  outputDir: 'packages',
  version: '1.0.0',
});
```

### Automated File Processing
```typescript
// File processing pipeline
const processingPipeline = [
  // Manifest validation and transformation
  validateManifest,
  transformManifestForBrowser,
  
  // Asset optimization
  optimizeImages,
  compressScripts,
  minifyCSS,
  
  // File filtering
  removeDevFiles,
  excludeSourceMaps,
  
  // Package creation
  createZipArchive,
  generateMetadata,
];
```

## Package Configuration

### Configuration Schema
```typescript
interface PackageConfig {
  // Target browser
  browser: 'chrome' | 'firefox' | 'opera' | 'edge';
  
  // Directories
  inputDir: string;
  outputDir: string;
  tempDir?: string;
  
  // Package metadata
  version: string;
  name?: string;
  description?: string;
  
  // Processing options
  optimize: boolean;
  validate: boolean;
  includeSourceMaps: boolean;
  
  // File filtering
  include: string[];
  exclude: string[];
  
  // Browser-specific options
  browserOptions?: {
    firefox?: FirefoxPackageOptions;
    chrome?: ChromePackageOptions;
    opera?: OperaPackageOptions;
  };
}
```

### Browser-Specific Options
```typescript
// Chrome Web Store options
interface ChromePackageOptions {
  manifestVersion: 2 | 3;
  keyFile?: string; // For development packaging
  updateUrl?: string;
  permissions?: {
    remove?: string[];
    add?: string[];
  };
}

// Firefox Add-ons options
interface FirefoxPackageOptions {
  strictMinVersion?: string;
  id?: string; // Extension ID
  signPackage?: boolean;
  addOnSdkVersion?: string;
}

// Opera Add-ons options
interface OperaPackageOptions {
  developerExtension?: boolean;
  packageFormat?: 'crx' | 'nex';
}
```

## File Processing

### Manifest Processing
```typescript
// Manifest transformation for different browsers
function transformManifest(manifest: any, browser: string) {
  const transformed = { ...manifest };
  
  switch (browser) {
    case 'firefox':
      // Remove unsupported Chrome features
      delete transformed.side_panel;
      delete transformed.action?.default_popup;
      
      // Add Firefox-specific fields
      transformed.browser_specific_settings = {
        gecko: {
          id: '<EMAIL>',
          strict_min_version: '109.0',
        },
      };
      break;
      
    case 'opera':
      // Add Opera sidebar support
      if (transformed.side_panel) {
        transformed.sidebar_action = {
          default_panel: transformed.side_panel.default_path,
          default_title: transformed.name,
        };
      }
      break;
  }
  
  return transformed;
}
```

### Asset Optimization
```typescript
// Image optimization
async function optimizeImages(inputDir: string) {
  const imageFiles = await glob(`${inputDir}/**/*.{png,jpg,jpeg,svg}`);
  
  for (const file of imageFiles) {
    await sharp(file)
      .png({ quality: 90, compressionLevel: 9 })
      .jpeg({ quality: 85, progressive: true })
      .toFile(file.replace(/\.[^.]+$/, '.optimized$&'));
  }
}

// Script minification
async function minifyScripts(inputDir: string) {
  const jsFiles = await glob(`${inputDir}/**/*.js`);
  
  for (const file of jsFiles) {
    const result = await terser.minify(
      await fs.readFile(file, 'utf8'),
      {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
        mangle: true,
      }
    );
    
    await fs.writeFile(file, result.code);
  }
}
```

### File Filtering
```typescript
// File filtering rules
const defaultExcludePatterns = [
  // Development files
  '**/*.map',
  '**/*.ts',
  '**/*.tsx',
  '**/tsconfig.json',
  
  // Build artifacts
  '**/.turbo',
  '**/node_modules',
  '**/.git',
  
  // IDE files
  '**/.vscode',
  '**/.idea',
  
  // Package files
  '**/package.json',
  '**/pnpm-lock.yaml',
];

const defaultIncludePatterns = [
  // Essential extension files
  'manifest.json',
  '**/*.js',
  '**/*.css',
  '**/*.html',
  '**/*.png',
  '**/*.jpg',
  '**/*.svg',
  
  // Localization
  '_locales/**/*',
];
```

## Usage Examples

### Basic Packaging
```typescript
// Simple package creation
import { zip } from '@extension/zipper';

await zip({
  browser: 'chrome',
  inputDir: './dist',
  outputDir: './packages',
  version: process.env.npm_package_version,
});
```

### Advanced Configuration
```typescript
// Advanced packaging with custom options
import { createPackage } from '@extension/zipper';

await createPackage({
  browser: 'chrome',
  inputDir: './dist',
  outputDir: './packages',
  version: '1.2.3',
  
  // Processing options
  optimize: true,
  validate: true,
  includeSourceMaps: false,
  
  // File filtering
  exclude: [
    '**/debug.js',
    '**/test-*',
    '**/*.spec.js',
  ],
  
  // Browser-specific options
  browserOptions: {
    chrome: {
      manifestVersion: 3,
      updateUrl: 'https://example.com/updates.xml',
    },
  },
});
```

### Multi-Browser Build Script
```javascript
// build-all.js
import { createPackage } from '@extension/zipper';

const browsers = ['chrome', 'firefox', 'opera'];
const version = process.env.npm_package_version;

for (const browser of browsers) {
  console.log(`📦 Packaging for ${browser}...`);
  
  await createPackage({
    browser,
    inputDir: `./dist-${browser}`,
    outputDir: './packages',
    version,
    optimize: true,
    validate: true,
  });
  
  console.log(`✅ ${browser} package created`);
}

console.log('🎉 All packages created successfully!');
```

## Build Integration

### Package.json Scripts
```json
{
  "scripts": {
    "zip": "pnpm build && pnpm -F zipper zip",
    "zip:chrome": "pnpm build && pnpm -F zipper zip --browser chrome",
    "zip:firefox": "pnpm build:firefox && pnpm -F zipper zip --browser firefox",
    "zip:all": "node scripts/build-all.js",
    "package": "pnpm zip:all"
  }
}
```

### GitHub Actions Integration
```yaml
# .github/workflows/package.yml
name: Package Extension
on:
  release:
    types: [published]

jobs:
  package:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Build and package
        run: pnpm package
        
      - name: Upload packages
        uses: actions/upload-artifact@v3
        with:
          name: extension-packages
          path: packages/*.zip
```

## Validation

### Manifest Validation
```typescript
// Validate manifest against browser requirements
async function validateManifest(manifest: any, browser: string) {
  const errors: ValidationError[] = [];
  
  // Common validations
  if (!manifest.manifest_version) {
    errors.push(new ValidationError('Missing manifest_version'));
  }
  
  if (!manifest.name) {
    errors.push(new ValidationError('Missing extension name'));
  }
  
  // Browser-specific validations
  switch (browser) {
    case 'firefox':
      if (manifest.manifest_version === 3 && !manifest.browser_specific_settings) {
        errors.push(new ValidationError('Firefox requires browser_specific_settings'));
      }
      break;
      
    case 'chrome':
      if (manifest.manifest_version === 2) {
        errors.push(new ValidationError('Chrome requires Manifest V3'));
      }
      break;
  }
  
  if (errors.length > 0) {
    throw new ValidationError('Manifest validation failed', errors);
  }
}
```

### Package Validation
```typescript
// Validate packaged extension
async function validatePackage(packagePath: string, browser: string) {
  const zip = new JSZip();
  const content = await fs.readFile(packagePath);
  const archive = await zip.loadAsync(content);
  
  // Check required files
  const requiredFiles = ['manifest.json'];
  for (const file of requiredFiles) {
    if (!archive.files[file]) {
      throw new ValidationError(`Missing required file: ${file}`);
    }
  }
  
  // Validate manifest in package
  const manifestContent = await archive.files['manifest.json'].async('text');
  const manifest = JSON.parse(manifestContent);
  await validateManifest(manifest, browser);
  
  console.log(`✅ Package validation successful for ${browser}`);
}
```

## Output Structure

### Generated Packages
```
packages/
├── drcode-ui-testing-chrome-v1.2.3.zip
├── drcode-ui-testing-firefox-v1.2.3.zip
├── drcode-ui-testing-opera-v1.2.3.zip
└── metadata/
    ├── chrome-manifest.json
    ├── firefox-manifest.json
    ├── opera-manifest.json
    └── package-info.json
```

### Package Metadata
```json
{
  "name": "drcode-ui-testing",
  "version": "1.2.3",
  "packages": {
    "chrome": {
      "filename": "drcode-ui-testing-chrome-v1.2.3.zip",
      "size": "2.4MB",
      "files": 127,
      "manifest_version": 3
    },
    "firefox": {
      "filename": "drcode-ui-testing-firefox-v1.2.3.zip",
      "size": "2.1MB",
      "files": 119,
      "manifest_version": 2
    }
  },
  "created": "2024-12-19T10:30:00Z"
}
```

This zipper package automates the entire packaging workflow, ensuring that the DrCode UI Testing extension can be easily distributed across multiple browser marketplaces with optimized, validated packages.
