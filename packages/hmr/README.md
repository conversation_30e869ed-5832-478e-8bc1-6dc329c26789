# @extension/hmr

## Overview
This package provides **Hot Module Replacement (HMR)** functionality for the DrCode UI Testing extension during development. It enables live reloading and hot swapping of modules without losing application state, significantly improving the development experience.

## Architecture
- **Browser Extension HMR**: Specialized HMR for Chrome extension contexts
- **Multi-Context Support**: Works across content scripts, background scripts, and extension pages
- **State Preservation**: Maintains application state during hot reloads
- **Development Only**: Completely excluded from production builds

## Key Features

### Extension-Specific HMR
Unlike traditional web HMR, this package handles the unique challenges of Chrome extensions:
- **Service Worker Reloading**: Background script hot reloading
- **Content Script Injection**: Live updates in injected scripts
- **Extension Page Updates**: Options page and popup hot reloading
- **Cross-Context Communication**: HMR coordination across extension contexts

### Module Hot Swapping
```typescript
// Hot module replacement integration
if (module.hot) {
  module.hot.accept('./TestComponent', () => {
    // Re-render component without losing state
    const NextTestComponent = require('./TestComponent').default;
    rerenderComponent(NextTestComponent);
  });
}
```

### State Preservation
```typescript
import { preserveState, restoreState } from '@extension/hmr';

// Before hot reload
const savedState = preserveState('test-runner', {
  currentTest: testData,
  results: testResults,
  settings: userSettings,
});

// After hot reload
const restoredState = restoreState('test-runner');
if (restoredState) {
  // Restore application state
  setTestData(restoredState.currentTest);
  setTestResults(restoredState.results);
  setUserSettings(restoredState.settings);
}
```

## HMR Contexts

### Background Script HMR
```typescript
// Background service worker hot reloading
import { backgroundHMR } from '@extension/hmr';

if (__DEV__) {
  backgroundHMR.enable({
    // Preserve service worker state
    preserveState: true,
    // Reload connected content scripts
    reloadContentScripts: true,
    // Maintain extension icon and badge
    preserveUI: true,
  });
}
```

### Content Script HMR
```typescript
// Content script hot reloading
import { contentScriptHMR } from '@extension/hmr';

if (__DEV__) {
  contentScriptHMR.enable({
    // Re-inject updated scripts
    reinject: true,
    // Preserve DOM modifications
    preserveDOM: true,
    // Maintain event listeners
    preserveListeners: true,
  });
}
```

### Extension Pages HMR
```typescript
// Options page, popup, and side panel HMR
import { extensionPageHMR } from '@extension/hmr';

if (__DEV__) {
  extensionPageHMR.enable({
    // Standard React HMR
    reactHMR: true,
    // Preserve form state
    preserveForms: true,
    // Maintain scroll position
    preserveScroll: true,
  });
}
```

## Development Workflow

### File Change Detection
```typescript
// Watch specific file patterns
import { createHMRWatcher } from '@extension/hmr';

const watcher = createHMRWatcher({
  patterns: [
    'src/**/*.tsx',
    'src/**/*.ts',
    'styles/**/*.css',
  ],
  exclude: [
    'node_modules/**',
    'dist/**',
  ],
});

watcher.on('change', (filePath) => {
  console.log(`File changed: ${filePath}`);
  // Trigger appropriate reload strategy
});
```

### Reload Strategies
```typescript
// Different reload strategies based on file type
import { reloadStrategy } from '@extension/hmr';

reloadStrategy.register('*.tsx', {
  type: 'hot-swap',
  preserveState: true,
});

reloadStrategy.register('*.css', {
  type: 'style-inject',
  preserveState: false,
});

reloadStrategy.register('manifest.json', {
  type: 'full-reload',
  preserveState: false,
});
```

## Configuration

### HMR Settings
```typescript
// HMR configuration
export interface HMRConfig {
  // Enable HMR (development only)
  enabled: boolean;
  
  // HMR server port
  port: number;
  
  // Reload delay (ms)
  reloadDelay: number;
  
  // State preservation options
  preserveState: {
    storage: boolean;
    components: boolean;
    network: boolean;
  };
  
  // Logging options
  logging: {
    verbose: boolean;
    showReloads: boolean;
    showPreservedState: boolean;
  };
}
```

### Environment Integration
```typescript
// Development environment detection
const hmrConfig: HMRConfig = {
  enabled: process.env.__DEV__ === 'true',
  port: parseInt(process.env.HMR_PORT || '3001'),
  reloadDelay: 100,
  preserveState: {
    storage: true,
    components: true,
    network: false,
  },
  logging: {
    verbose: process.env.HMR_VERBOSE === 'true',
    showReloads: true,
    showPreservedState: false,
  },
};
```

## Usage Examples

### React Component HMR
```typescript
import React, { useState } from 'react';
import { useHMRState } from '@extension/hmr';

function TestRunner() {
  // State that survives hot reloads
  const [testResults, setTestResults] = useHMRState('test-results', []);
  const [currentTest, setCurrentTest] = useHMRState('current-test', null);
  
  // Regular state (will be reset on hot reload)
  const [isLoading, setIsLoading] = useState(false);
  
  return (
    <div>
      <h1>Test Runner</h1>
      {/* Component UI */}
    </div>
  );
}

// Enable HMR for this component
if (module.hot) {
  module.hot.accept();
}

export default TestRunner;
```

### CSS Hot Reloading
```typescript
// CSS hot reloading
import { cssHMR } from '@extension/hmr';

if (__DEV__) {
  cssHMR.enable({
    // Inject updated styles without page reload
    injectStyles: true,
    // Remove old styles
    removeOldStyles: true,
    // Preserve CSS custom properties
    preserveCustomProperties: true,
  });
}
```

### Asset Hot Reloading
```typescript
// Asset hot reloading (images, icons, etc.)
import { assetHMR } from '@extension/hmr';

if (__DEV__) {
  assetHMR.enable({
    // Reload changed images
    images: true,
    // Update extension icons
    icons: true,
    // Refresh manifests
    manifests: true,
  });
}
```

## Build Integration

### Vite Plugin
```javascript
// vite.config.js
import { extensionHMR } from '@extension/hmr/vite';

export default {
  plugins: [
    extensionHMR({
      // HMR configuration
      port: 3001,
      contexts: ['background', 'content', 'pages'],
    }),
  ],
};
```

### Webpack Plugin
```javascript
// webpack.config.js
const { ExtensionHMRPlugin } = require('@extension/hmr/webpack');

module.exports = {
  plugins: [
    new ExtensionHMRPlugin({
      port: 3001,
      reloadDelay: 100,
    }),
  ],
};
```

## Performance Considerations

### Selective Reloading
```typescript
// Only reload affected modules
import { selectiveReload } from '@extension/hmr';

selectiveReload.configure({
  // Dependency graph analysis
  analyzeDependencies: true,
  // Only reload affected components
  cascadeReload: false,
  // Batch multiple changes
  debounceReload: 200,
});
```

### Memory Management
```typescript
// Clean up HMR resources
import { hmrCleanup } from '@extension/hmr';

// Cleanup on extension unload
chrome.runtime.onSuspend.addListener(() => {
  hmrCleanup.dispose();
});
```

This HMR package dramatically improves the development experience by providing fast, reliable hot reloading specifically tailored for Chrome extension development, while maintaining application state and context across reloads.
