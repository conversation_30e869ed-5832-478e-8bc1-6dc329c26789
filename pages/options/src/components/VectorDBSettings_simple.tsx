import { useState, useEffect } from 'react';

interface VectorDBSettingsProps {
  isDarkMode?: boolean;
}

export const VectorDBSettings = ({ isDarkMode = false }: VectorDBSettingsProps) => {
  console.log('VectorDBSettings component rendering...');

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('VectorDBSettings: useEffect running');
    // Simulate some loading
    setTimeout(() => {
      setIsLoading(false);
      console.log('VectorDBSettings: loading complete');
    }, 1000);
  }, []);

  try {
    console.log('VectorDBSettings: rendering, isLoading:', isLoading, 'error:', error);

    return (
      <div className="space-y-6 p-6">
        <div>
          <h2 className={`mb-4 text-2xl font-bold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Vector Database Management (Debug Mode)
          </h2>

          {isLoading ? (
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Loading component...</p>
            </div>
          ) : (
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                ✅ Component loaded successfully! This is a simplified debug version.
              </p>
              <p className={`text-sm mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                If you can see this message, the component is rendering without errors.
              </p>
            </div>
          )}

          {error && (
            <div
              className={`mt-4 p-4 rounded-lg ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
              <p className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>Error: {error}</p>
            </div>
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error('VectorDBSettings: render error:', error);
    return (
      <div className="space-y-6 p-6">
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
          <h2 className={`mb-2 text-lg font-semibold ${isDarkMode ? 'text-red-400' : 'text-red-800'}`}>
            Component Error
          </h2>
          <p className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>
            An error occurred while rendering: {error instanceof Error ? error.message : 'Unknown error'}
          </p>
        </div>
      </div>
    );
  }
};
