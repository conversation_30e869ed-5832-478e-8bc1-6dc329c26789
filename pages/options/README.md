# Options Page

## Overview
The **options page** provides a comprehensive settings and configuration interface for the DrCode UI Testing extension. It serves as the central hub for user preferences, API configurations, test templates, and extension customization.

## Architecture
- **React Application**: Full-featured React app with modern hooks
- **Settings Management**: Persistent storage of user preferences
- **API Integration**: Configuration for multiple AI providers
- **Template System**: Manageable test templates and snippets

## Key Features

### API Configuration
**Purpose**: Configure AI provider credentials and settings

```tsx
// API configuration interface
function APISettings() {
  const [apiKeys, setApiKeys] = useStorageState('apiKeys', {});
  const [selectedProvider, setSelectedProvider] = useStorageState('aiProvider', 'openai');

  const providers = [
    { id: 'gemini', name: 'Google Gemini', description: 'Gemini 2.5 Pro, Gemini 2.5 Flash models' },
    { id: 'anthropic', name: 'Anthropic', description: 'Claude models' },
    { id: 'google', name: 'Google AI', description: 'Gemini models' },
    { id: 'groq', name: '<PERSON><PERSON><PERSON>', description: 'Fast inference models' },
    { id: 'ollama', name: '<PERSON><PERSON><PERSON>', description: 'Local AI models' },
  ];

  return (
    <Card>
      <CardHeader>
        <h2>AI Provider Configuration</h2>
        <p>Configure your preferred AI provider for test generation and analysis</p>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-6">
          {providers.map((provider) => (
            <div key={provider.id} className="border rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Radio
                  name="aiProvider"
                  value={provider.id}
                  checked={selectedProvider === provider.id}
                  onChange={(e) => setSelectedProvider(e.target.value)}
                />
                <div>
                  <h3 className="font-semibold">{provider.name}</h3>
                  <p className="text-sm text-gray-600">{provider.description}</p>
                </div>
              </div>
              
              {selectedProvider === provider.id && (
                <div className="mt-4">
                  <Input
                    label="API Key"
                    type="password"
                    placeholder={`Enter your ${provider.name} API key`}
                    value={apiKeys[provider.id] || ''}
                    onChange={(e) => setApiKeys({ ...apiKeys, [provider.id]: e.target.value })}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}
```

### Test Templates
**Purpose**: Manage reusable test templates and patterns

```tsx
// Test template management
function TestTemplates() {
  const [templates, setTemplates] = useStorageState('testTemplates', []);
  const [isCreating, setIsCreating] = useState(false);

  const defaultTemplates = [
    {
      name: 'Login Form Test',
      description: 'Test standard login functionality',
      steps: [
        { action: 'type', selector: 'input[name="email"]', value: '{{email}}' },
        { action: 'type', selector: 'input[name="password"]', value: '{{password}}' },
        { action: 'click', selector: 'button[type="submit"]' },
        { action: 'assert', selector: '.success-message', assertion: 'visible' }
      ]
    },
    {
      name: 'Form Validation Test',
      description: 'Test form validation messages',
      steps: [
        { action: 'click', selector: 'button[type="submit"]' },
        { action: 'assert', selector: '.error-message', assertion: 'visible' },
        { action: 'assert', selector: '.error-message', assertion: 'contains', value: 'required' }
      ]
    },
    {
      name: 'Navigation Test',
      description: 'Test page navigation and routing',
      steps: [
        { action: 'click', selector: 'a[href="/about"]' },
        { action: 'wait', selector: 'h1', timeout: 3000 },
        { action: 'assert', selector: 'h1', assertion: 'contains', value: 'About' }
      ]
    }
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <h2>Test Templates</h2>
            <p>Create and manage reusable test patterns</p>
          </div>
          <Button onClick={() => setIsCreating(true)}>
            <PlusIcon className="w-4 h-4 mr-2" />
            New Template
          </Button>
        </div>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-4">
          {templates.map((template, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold">{template.name}</h3>
                  <p className="text-sm text-gray-600">{template.description}</p>
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {template.steps.length} steps
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="secondary" size="sm">Edit</Button>
                  <Button variant="secondary" size="sm">Use</Button>
                  <Button variant="danger" size="sm">Delete</Button>
                </div>
              </div>
            </div>
          ))}
          
          {templates.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No custom templates yet.</p>
              <Button 
                variant="secondary" 
                className="mt-2"
                onClick={() => setTemplates(defaultTemplates)}
              >
                Load Default Templates
              </Button>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
}
```

### General Settings
**Purpose**: Configure extension behavior and preferences

```tsx
// General extension settings
function GeneralSettings() {
  const [settings, setSettings] = useStorageState('extensionSettings', {
    theme: 'system',
    autoSave: true,
    debugMode: false,
    screenshotMode: 'on-failure',
    testTimeout: 30000,
    retryAttempts: 3,
    showNotifications: true,
  });

  const updateSetting = (key: string, value: any) => {
    setSettings({ ...settings, [key]: value });
  };

  return (
    <Card>
      <CardHeader>
        <h2>General Settings</h2>
        <p>Configure extension behavior and preferences</p>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-6">
          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium mb-2">Theme</label>
            <Select
              value={settings.theme}
              onChange={(value) => updateSetting('theme', value)}
              options={[
                { value: 'light', label: 'Light' },
                { value: 'dark', label: 'Dark' },
                { value: 'system', label: 'System' },
              ]}
            />
          </div>

          {/* Auto Save */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Auto Save Tests</label>
              <p className="text-sm text-gray-600">Automatically save test progress</p>
            </div>
            <Switch
              checked={settings.autoSave}
              onChange={(checked) => updateSetting('autoSave', checked)}
            />
          </div>

          {/* Debug Mode */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Debug Mode</label>
              <p className="text-sm text-gray-600">Enable detailed logging and debug info</p>
            </div>
            <Switch
              checked={settings.debugMode}
              onChange={(checked) => updateSetting('debugMode', checked)}
            />
          </div>

          {/* Screenshot Mode */}
          <div>
            <label className="block text-sm font-medium mb-2">Screenshot Capture</label>
            <Select
              value={settings.screenshotMode}
              onChange={(value) => updateSetting('screenshotMode', value)}
              options={[
                { value: 'never', label: 'Never' },
                { value: 'on-failure', label: 'On Failure Only' },
                { value: 'always', label: 'Always' },
              ]}
            />
          </div>

          {/* Test Timeout */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Test Timeout ({settings.testTimeout / 1000}s)
            </label>
            <Slider
              min={5000}
              max={120000}
              step={5000}
              value={settings.testTimeout}
              onChange={(value) => updateSetting('testTimeout', value)}
            />
          </div>

          {/* Retry Attempts */}
          <div>
            <label className="block text-sm font-medium mb-2">Retry Attempts</label>
            <Input
              type="number"
              min="0"
              max="10"
              value={settings.retryAttempts}
              onChange={(e) => updateSetting('retryAttempts', parseInt(e.target.value))}
            />
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
```

## Page Layout

### Navigation Structure
```tsx
// Main options page layout
function OptionsPage() {
  const [activeTab, setActiveTab] = useState('general');

  const tabs = [
    { id: 'general', label: 'General', icon: SettingsIcon },
    { id: 'api', label: 'API Keys', icon: KeyIcon },
    { id: 'templates', label: 'Templates', icon: DocumentIcon },
    { id: 'advanced', label: 'Advanced', icon: CogIcon },
    { id: 'about', label: 'About', icon: InfoIcon },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <img className="h-8 w-8" src="/icon-32.png" alt="DrCode" />
              <h1 className="ml-3 text-xl font-semibold text-gray-900 dark:text-white">
                DrCode: UI Testing Settings
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Button variant="secondary" onClick={exportSettings}>
                <DownloadIcon className="w-4 h-4 mr-2" />
                Export Settings
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex">
          {/* Sidebar Navigation */}
          <nav className="w-64 space-y-1 mr-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'w-full flex items-center px-3 py-2 text-sm font-medium rounded-md',
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800'
                )}
              >
                <tab.icon className="w-5 h-5 mr-3" />
                {tab.label}
              </button>
            ))}
          </nav>

          {/* Main Content */}
          <main className="flex-1">
            {activeTab === 'general' && <GeneralSettings />}
            {activeTab === 'api' && <APISettings />}
            {activeTab === 'templates' && <TestTemplates />}
            {activeTab === 'advanced' && <AdvancedSettings />}
            {activeTab === 'about' && <AboutPage />}
          </main>
        </div>
      </div>
    </div>
  );
}
```

## Advanced Features

### Data Import/Export
```tsx
// Settings backup and restore
function DataManagement() {
  const exportSettings = async () => {
    const settings = await storage.get([
      'extensionSettings',
      'apiKeys',
      'testTemplates',
      'userPreferences'
    ]);
    
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: settings
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `drcode-settings-${Date.now()}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const importSettings = async (file: File) => {
    try {
      const text = await file.text();
      const importData = JSON.parse(text);
      
      if (importData.version && importData.settings) {
        await storage.set(importData.settings);
        showNotification('Settings imported successfully');
      } else {
        throw new Error('Invalid settings file format');
      }
    } catch (error) {
      showNotification('Failed to import settings: ' + error.message, 'error');
    }
  };

  return (
    <Card>
      <CardHeader>
        <h2>Data Management</h2>
        <p>Backup and restore your extension settings</p>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-4">
          <Button onClick={exportSettings}>
            <DownloadIcon className="w-4 h-4 mr-2" />
            Export Settings
          </Button>
          
          <div>
            <input
              type="file"
              accept=".json"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) importSettings(file);
              }}
              className="hidden"
              id="import-file"
            />
            <Button
              variant="secondary"
              onClick={() => document.getElementById('import-file')?.click()}
            >
              <UploadIcon className="w-4 h-4 mr-2" />
              Import Settings
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
```

### Theme Management
```tsx
// Theme switching functionality
function ThemeToggle() {
  const [theme, setTheme] = useStorageState('theme', 'system');

  useEffect(() => {
    const root = window.document.documentElement;
    
    if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  return (
    <Select
      value={theme}
      onChange={setTheme}
      options={[
        { value: 'light', label: '☀️ Light' },
        { value: 'dark', label: '🌙 Dark' },
        { value: 'system', label: '💻 System' },
      ]}
    />
  );
}
```

## Integration with Extension

### Settings Synchronization
The options page automatically synchronizes all settings with other extension components through the `@extension/storage` package, ensuring that changes are immediately available to the side panel, content script, and background script.

### Real-time Updates
Settings changes are broadcast to other extension components via Chrome's storage change events, allowing for immediate application of new configurations without requiring extension reload.

### Validation and Error Handling
All user inputs are validated client-side and server-side (for API keys), with clear error messages and guidance for resolution.

This options page provides a comprehensive configuration interface that makes the DrCode UI Testing extension highly customizable while maintaining ease of use for both novice and advanced users.
