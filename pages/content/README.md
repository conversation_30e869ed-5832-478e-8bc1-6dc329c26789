# Content Script

## Overview
The **content script** is the core component that enables DrCode UI Testing to interact with web pages. It runs in the context of web pages, providing DOM manipulation, element selection, test execution, and real-time feedback capabilities.

## Architecture
- **Injected Script**: Runs in the page context with full DOM access
- **Isolated Environment**: Maintains separation from page scripts
- **Message Bridge**: Communicates with background script and side panel
- **UI Overlay**: Provides non-intrusive testing interface

## Key Responsibilities

### DOM Interaction
- **Element Selection**: Interactive element picker and selector generation
- **DOM Manipulation**: Clicking, typing, scrolling, and other user actions
- **State Inspection**: Reading element properties, attributes, and content
- **Event Simulation**: Triggering browser events programmatically

### Test Execution
- **Step Processing**: Execute individual test steps sequentially
- **Error Handling**: Capture and report execution errors
- **Screenshot Capture**: Take screenshots at key test points
- **Performance Monitoring**: Track test execution timing

### Visual Feedback
- **Overlay Interface**: Floating UI for test controls and status
- **Element Highlighting**: Visual indicators for selected elements
- **Progress Display**: Real-time test execution progress
- **Error Visualization**: Highlight failed elements and actions

## Features

### Element Selector Tool
```typescript
// Interactive element selection
class ElementSelector {
  private isActive = false;
  private highlightedElement: HTMLElement | null = null;

  activate() {
    this.isActive = true;
    document.addEventListener('mouseover', this.handleMouseOver);
    document.addEventListener('click', this.handleClick);
    this.showSelectorUI();
  }

  private handleMouseOver = (event: MouseEvent) => {
    if (!this.isActive) return;
    
    this.clearHighlight();
    this.highlightedElement = event.target as HTMLElement;
    this.highlightElement(this.highlightedElement);
  };

  private handleClick = (event: MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    const selector = this.generateSelector(event.target as HTMLElement);
    this.sendSelectorToSidePanel(selector);
    this.deactivate();
  };

  private generateSelector(element: HTMLElement): string {
    // Generate unique CSS selector
    const selectors = [
      this.getIdSelector(element),
      this.getClassSelector(element),
      this.getAttributeSelector(element),
      this.getPositionalSelector(element),
    ].filter(Boolean);
    
    return selectors[0] || this.getTagSelector(element);
  }
}
```

### Test Step Execution
```typescript
// Execute test steps
class TestExecutor {
  async executeStep(step: TestStep): Promise<StepResult> {
    try {
      const element = await this.findElement(step.selector);
      
      switch (step.action) {
        case 'click':
          return await this.clickElement(element, step);
        case 'type':
          return await this.typeText(element, step);
        case 'wait':
          return await this.waitForElement(step);
        case 'assert':
          return await this.assertElement(element, step);
        default:
          throw new Error(`Unknown action: ${step.action}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        screenshot: await this.captureScreenshot(),
      };
    }
  }

  private async clickElement(element: HTMLElement, step: TestStep): Promise<StepResult> {
    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Wait for element to be clickable
    await this.waitForClickable(element);
    
    // Highlight element
    this.highlightElement(element);
    
    // Perform click
    element.click();
    
    // Capture result
    return {
      success: true,
      element: this.getElementInfo(element),
      screenshot: await this.captureScreenshot(),
    };
  }

  private async typeText(element: HTMLInputElement, step: TestStep): Promise<StepResult> {
    // Focus element
    element.focus();
    
    // Clear existing text
    element.value = '';
    
    // Type text character by character (human-like)
    for (const char of step.value) {
      element.value += char;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50); // Human-like typing speed
    }
    
    // Trigger change event
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    return {
      success: true,
      element: this.getElementInfo(element),
      value: element.value,
    };
  }
}
```

### Overlay Interface
```typescript
// Floating test control overlay
class TestOverlay {
  private overlay: HTMLElement;

  constructor() {
    this.createOverlay();
    this.setupEventListeners();
  }

  private createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.id = 'drcode-test-overlay';
    this.overlay.innerHTML = `
      <div class="drcode-overlay-header">
        <h3>DrCode Testing</h3>
        <button id="drcode-minimize">−</button>
      </div>
      <div class="drcode-overlay-content">
        <div id="drcode-status">Ready</div>
        <div id="drcode-progress"></div>
        <div class="drcode-controls">
          <button id="drcode-start">Start Test</button>
          <button id="drcode-stop">Stop Test</button>
          <button id="drcode-selector">Select Element</button>
        </div>
      </div>
    `;

    // Inject styles
    this.injectStyles();
    document.body.appendChild(this.overlay);
  }

  private injectStyles() {
    const styles = `
      #drcode-test-overlay {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 280px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-family: system-ui, sans-serif;
        font-size: 14px;
      }
      
      .drcode-overlay-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #3b82f6;
        color: white;
        border-radius: 8px 8px 0 0;
      }
      
      .drcode-overlay-content {
        padding: 16px;
      }
      
      .drcode-controls button {
        margin: 4px;
        padding: 8px 12px;
        border: none;
        border-radius: 4px;
        background: #f3f4f6;
        cursor: pointer;
      }
      
      .drcode-controls button:hover {
        background: #e5e7eb;
      }
      
      .drcode-element-highlight {
        outline: 2px solid #3b82f6 !important;
        outline-offset: 2px !important;
        background-color: rgba(59, 130, 246, 0.1) !important;
      }
    `;

    const styleSheet = document.createElement('style');
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);
  }

  updateStatus(status: string) {
    const statusElement = this.overlay.querySelector('#drcode-status');
    if (statusElement) {
      statusElement.textContent = status;
    }
  }

  updateProgress(current: number, total: number) {
    const progressElement = this.overlay.querySelector('#drcode-progress');
    if (progressElement) {
      const percentage = Math.round((current / total) * 100);
      progressElement.innerHTML = `
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${percentage}%"></div>
        </div>
        <span>${current}/${total} steps completed</span>
      `;
    }
  }
}
```

## Communication Layer

### Message Handling
```typescript
// Communication with background script and side panel
class MessageHandler {
  constructor() {
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
  }

  private async handleMessage(message: any, sender: any, sendResponse: Function) {
    switch (message.type) {
      case 'START_TEST':
        await this.startTest(message.testData);
        break;
        
      case 'STOP_TEST':
        this.stopTest();
        break;
        
      case 'SELECT_ELEMENT':
        this.activateElementSelector();
        break;
        
      case 'EXECUTE_STEP':
        const result = await this.executeStep(message.step);
        sendResponse(result);
        break;
    }
  }

  sendMessage(type: string, data: any) {
    chrome.runtime.sendMessage({
      type,
      data,
      tabId: await this.getCurrentTabId(),
    });
  }

  private async getCurrentTabId(): Promise<number> {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_TAB_ID' }, resolve);
    });
  }
}
```

### State Synchronization
```typescript
// Sync state with other extension components
class StateManager {
  private state: ContentScriptState = {
    isTestRunning: false,
    currentStep: null,
    selectedElement: null,
    testResults: [],
  };

  updateState(updates: Partial<ContentScriptState>) {
    this.state = { ...this.state, ...updates };
    this.syncToSidePanel();
  }

  private syncToSidePanel() {
    chrome.runtime.sendMessage({
      type: 'STATE_UPDATE',
      state: this.state,
    });
  }
}
```

## Integration Points

### Side Panel Integration
- Receives test commands and step execution requests
- Sends test results and element selection data
- Synchronizes test progress and status updates

### Background Script Integration
- Coordinates with other tabs and extension components
- Manages extension lifecycle and permissions
- Handles cross-tab communication

### Options Page Integration
- Applies user preferences and settings
- Uses configured AI providers and API keys
- Respects debugging and logging preferences

## Usage Examples

### Manual Element Selection
1. User clicks "Select Element" in side panel
2. Content script activates element selector
3. User hovers over page elements (highlighted in blue)
4. User clicks desired element
5. Generated selector sent back to side panel

### Automated Test Execution
1. Side panel sends test definition to content script
2. Content script executes steps sequentially
3. Each step result reported back to side panel
4. Screenshots captured at key points
5. Final test report generated and displayed

## Error Handling

### Graceful Degradation
- Handle missing elements with retry logic
- Provide fallback selectors when primary fails
- Capture detailed error information for debugging
- Continue test execution when possible

### User Feedback
- Visual indicators for successful/failed actions
- Clear error messages in overlay interface
- Screenshot evidence for failed assertions
- Detailed logs for troubleshooting

This content script serves as the bridge between the DrCode UI Testing extension and the web pages being tested, providing comprehensive automation capabilities while maintaining a clean, non-intrusive user experience.
