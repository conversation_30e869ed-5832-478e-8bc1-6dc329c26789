# Pages

## Overview
This directory contains the **user interface pages** of the DrCode UI Testing extension. Each subdirectory represents a different context where the extension's UI is rendered, including content scripts, options pages, and side panels.

## Architecture
- **Multi-Context UI**: Different interfaces for various extension contexts
- **React-Based**: Modern React applications with TypeScript
- **Shared Components**: Leverages `@extension/ui` for consistent design
- **Context-Specific**: Each page optimized for its specific use case

## Page Structure

### `/content`
**Purpose**: Content script that runs on web pages
- **Context**: Injected into web pages for DOM interaction
- **Functionality**: Test execution, element selection, UI overlay
- **Permissions**: Full page access for testing automation

### `/options`
**Purpose**: Extension settings and configuration page
- **Context**: Standalone extension page (`chrome-extension://` URL)
- **Functionality**: User preferences, API key management, test templates
- **Access**: Available via extension management or context menu

### `/side-panel`
**Purpose**: Chrome's side panel interface (primary testing UI)
- **Context**: Chrome side panel API (Chrome 114+)
- **Functionality**: Main testing interface, test management, results display
- **Integration**: Persistent alongside web pages during testing

## Common Patterns

### Shared Dependencies
All pages share common workspace packages:
```json
{
  "dependencies": {
    "@extension/shared": "workspace:*",    // Common utilities and hooks
    "@extension/storage": "workspace:*",   // Data persistence
    "@extension/ui": "workspace:*",        // UI component library
    "@extension/i18n": "workspace:*"       // Internationalization
  }
}
```

### Build Configuration
Each page uses Vite with extension-specific configurations:
```typescript
// Typical vite.config.mts
import { defineConfig } from 'vite';
import { createPageConfig } from '@extension/vite-config';

export default defineConfig(
  createPageConfig({
    entry: 'src/main.tsx',
    outDir: '../../dist/page-name',
  })
);
```

### TypeScript Setup
Consistent TypeScript configuration across all pages:
```json
{
  "extends": "@extension/tsconfig/app.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@extension/*": ["../../packages/*/src"]
    }
  }
}
```

## Development Workflow

### Individual Development
```bash
# Develop specific page
cd pages/side-panel
pnpm dev

# Build specific page
pnpm build

# Type check
pnpm type-check
```

### Integrated Development
```bash
# From project root - develop all pages
pnpm dev

# Build all pages
pnpm build

# Run all type checks
pnpm type-check
```

## Inter-Page Communication

### Message Passing
Pages communicate through Chrome extension messaging APIs:

```typescript
// From content script to side panel
chrome.runtime.sendMessage({
  type: 'TEST_RESULT',
  data: testResult,
});

// In side panel
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'TEST_RESULT') {
    updateTestResults(message.data);
  }
});
```

### Shared State Management
Using `@extension/storage` for persistent state:

```typescript
// Update shared state
import { storage } from '@extension/storage';

await storage.set('currentTest', testData);

// Listen for state changes across pages
storage.onChanged.addListener((changes) => {
  if (changes.currentTest) {
    handleTestUpdate(changes.currentTest.newValue);
  }
});
```

## User Experience Flow

### Testing Workflow
1. **Options Page**: User configures API keys and preferences
2. **Side Panel**: User creates and manages tests
3. **Content Script**: Executes tests on target web pages
4. **Side Panel**: Displays results and provides test controls

### Navigation Patterns
- **Options**: Accessed via extension icon → Options
- **Side Panel**: Chrome's native side panel (F12 → Side Panel)
- **Content Script**: Automatically injected, provides overlay UI

## Responsive Design

### Viewport Constraints
Each page optimized for its container:

```css
/* Side panel - narrow vertical layout */
.side-panel-layout {
  width: 320px;
  height: 100vh;
  overflow-y: auto;
}

/* Options page - full browser tab */
.options-layout {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Content script overlay - minimal footprint */
.content-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  z-index: 10000;
}
```

## Testing Strategy

### Page-Specific Testing
```typescript
// Testing content script functionality
describe('Content Script', () => {
  it('should inject overlay on page load', () => {
    // Test DOM injection
  });
  
  it('should communicate with background script', () => {
    // Test message passing
  });
});

// Testing side panel functionality
describe('Side Panel', () => {
  it('should load test list', () => {
    // Test React component rendering
  });
  
  it('should handle test execution', () => {
    // Test user interactions
  });
});
```

### Cross-Page Integration Testing
```typescript
// Test communication between pages
describe('Page Communication', () => {
  it('should sync state between side panel and content script', () => {
    // Test state synchronization
  });
  
  it('should persist settings from options page', () => {
    // Test settings persistence
  });
});
```

## Performance Considerations

### Bundle Optimization
- **Code Splitting**: Separate chunks for different features
- **Tree Shaking**: Remove unused code from builds
- **Lazy Loading**: Load components on demand

### Memory Management
- **Event Cleanup**: Remove listeners on component unmount
- **State Cleanup**: Clear temporary data when not needed
- **DOM Cleanup**: Remove injected elements properly

## Browser Compatibility

### Chrome Features
- Side panel API (Chrome 114+)
- Service worker background scripts
- Manifest V3 APIs

### Firefox Compatibility
- No side panel support (falls back to popup)
- Background page instead of service worker
- Manifest V2 compatibility mode

This pages structure provides a comprehensive user interface system that covers all interaction points of the DrCode UI Testing extension, ensuring seamless user experience across different contexts.
