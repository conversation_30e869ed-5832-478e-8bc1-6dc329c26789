import React, { useState } from 'react';
import { FiUpload, FiX, FiFileText, FiCheckCircle, FiAlertCircle } from 'react-icons/fi';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { getBackendUrl } from '@extension/storage';

interface DocumentEvaluationProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode?: boolean;
}

type QualitativeScore = 'Bad' | 'Fair' | 'Good' | 'Excellent';

interface FlowScore {
  flow_name: string;
  score: number | QualitativeScore;
  ui_element_mapping: number | QualitativeScore;
  flow_coverage: number | QualitativeScore;
  action_intent_mapping: number | QualitativeScore;
  structure: number | QualitativeScore;
  ambiguity_noise: number | QualitativeScore;
  recommendations: string[];
}

interface EvaluationResult {
  success: boolean;
  overall_score?: number | QualitativeScore;
  flow_scores?: FlowScore[];
  global_recommendations?: string[];
  error?: string;
  filename?: string;
}

const DocumentEvaluationModal: React.FC<DocumentEvaluationProps> = ({ isOpen, onClose, isDarkMode = false }) => {
  const [dragActive, setDragActive] = useState(false);
  const [isEvaluating, setIsEvaluating] = useState(false);
  const [evaluationResult, setEvaluationResult] = useState<EvaluationResult | null>(null);
  const [textInput, setTextInput] = useState('');
  const [inputMode, setInputMode] = useState<'file' | 'text'>('file');

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    const allowedTypes = [
      'text/plain',
      'text/markdown',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    const allowedExtensions = ['.txt', '.md', '.docx'];

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      alert('Please upload a .txt, .md, or .docx file');
      return;
    }

    setIsEvaluating(true);
    setEvaluationResult(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const backendUrl = await getBackendUrl();
      const response = await fetch(`${backendUrl}/api/v1/evaluate-document-file`, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      setEvaluationResult(result);
    } catch (error) {
      console.error('Error evaluating document:', error);
      setEvaluationResult({
        success: false,
        error: 'Failed to evaluate document. Please try again.',
      });
    } finally {
      setIsEvaluating(false);
    }
  };

  const handleTextEvaluation = async () => {
    if (!textInput.trim()) {
      alert('Please enter some text to evaluate');
      return;
    }

    setIsEvaluating(true);
    setEvaluationResult(null);

    try {
      const backendUrl = await getBackendUrl();
      const response = await fetch(`${backendUrl}/api/v1/evaluate-document`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document_text: textInput,
        }),
      });

      const result = await response.json();
      setEvaluationResult(result);
    } catch (error) {
      console.error('Error evaluating document:', error);
      setEvaluationResult({
        success: false,
        error: 'Failed to evaluate document. Please try again.',
      });
    } finally {
      setIsEvaluating(false);
    }
  };

  const isQualitativeScore = (score: number | QualitativeScore): score is QualitativeScore => {
    return typeof score === 'string';
  };

  const getQualitativeScoreColor = (score: QualitativeScore) => {
    switch (score) {
      case 'Excellent': return 'text-green-500';
      case 'Good': return 'text-green-400';
      case 'Fair': return 'text-yellow-500';
      case 'Bad': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getScoreColor = (score: number | QualitativeScore, maxScore?: number) => {
    if (isQualitativeScore(score)) {
      return getQualitativeScoreColor(score);
    }
    const percentage = maxScore ? (score / maxScore) * 100 : score;
    if (percentage >= 80) return 'text-green-500';
    if (percentage >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getOverallScoreColor = (score: number | QualitativeScore) => {
    if (isQualitativeScore(score)) {
      return getQualitativeScoreColor(score);
    }
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const formatScore = (score: number | QualitativeScore, maxScore?: number) => {
    if (isQualitativeScore(score)) {
      return score;
    }
    return maxScore ? `${score.toFixed(1)}/${maxScore}` : `${score.toFixed(1)}/100`;
  };

  const getScoreThreshold = (score: number | QualitativeScore, threshold: number) => {
    if (isQualitativeScore(score)) {
      // Map qualitative scores to numerical equivalents for threshold comparison
      const scoreMap = { 'Bad': 40, 'Fair': 60, 'Good': 80, 'Excellent': 95 };
      return scoreMap[score] >= threshold;
    }
    return score >= threshold;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className={`w-full max-w-4xl max-h-[90vh] rounded-lg shadow-lg overflow-hidden ${
          isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'
        }`}>
        {/* Header */}
        <div
          className={`px-6 py-4 border-b flex items-center justify-between ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
          <h2 className="text-xl font-semibold flex items-center">
            <FiFileText className="mr-2" />
            Document Evaluation for UI Testing
          </h2>
          <button
            onClick={onClose}
            className={`p-2 rounded-full hover:bg-opacity-10 hover:bg-gray-500 ${
              isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'
            }`}>
            <FiX size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {!evaluationResult && (
            <>
              {/* Mode Selection */}
              <div className="mb-6">
                <div className="flex space-x-4 mb-4">
                  <button
                    onClick={() => setInputMode('file')}
                    className={`px-4 py-2 rounded-lg ${
                      inputMode === 'file'
                        ? isDarkMode
                          ? 'bg-blue-600 text-white'
                          : 'bg-blue-500 text-white'
                        : isDarkMode
                          ? 'bg-gray-700 text-gray-300'
                          : 'bg-gray-200 text-gray-700'
                    }`}>
                    Upload File
                  </button>
                  <button
                    onClick={() => setInputMode('text')}
                    className={`px-4 py-2 rounded-lg ${
                      inputMode === 'text'
                        ? isDarkMode
                          ? 'bg-blue-600 text-white'
                          : 'bg-blue-500 text-white'
                        : isDarkMode
                          ? 'bg-gray-700 text-gray-300'
                          : 'bg-gray-200 text-gray-700'
                    }`}>
                    Paste Text
                  </button>
                </div>
              </div>

              {inputMode === 'file' ? (
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center ${
                    dragActive
                      ? isDarkMode
                        ? 'border-blue-400 bg-blue-900 bg-opacity-20'
                        : 'border-blue-400 bg-blue-50'
                      : isDarkMode
                        ? 'border-gray-600 hover:border-gray-500'
                        : 'border-gray-300 hover:border-gray-400'
                  } ${isEvaluating ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                  onClick={() => document.getElementById('file-input')?.click()}>
                  <input
                    id="file-input"
                    type="file"
                    accept=".txt,.md,.docx"
                    onChange={handleFileInput}
                    className="hidden"
                    disabled={isEvaluating}
                  />

                  {isEvaluating ? (
                    <div className="flex flex-col items-center">
                      <AiOutlineLoading3Quarters className="animate-spin mb-4" size={48} />
                      <p className="text-lg font-medium">Evaluating Document...</p>
                      <p className="text-sm opacity-75 mt-2">This may take a few moments</p>
                    </div>
                  ) : (
                    <div>
                      <FiUpload className="mx-auto mb-4" size={48} />
                      <p className="text-lg font-medium mb-2">
                        {dragActive ? 'Drop your document here' : 'Upload a document for evaluation'}
                      </p>
                      <p className="text-sm opacity-75">Supported formats: .txt, .md, .docx</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <textarea
                    value={textInput}
                    onChange={e => setTextInput(e.target.value)}
                    placeholder="Paste your document content here..."
                    className={`w-full h-64 p-4 rounded-lg border resize-none ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-800 placeholder-gray-500'
                    }`}
                    disabled={isEvaluating}
                  />
                  <button
                    onClick={handleTextEvaluation}
                    disabled={isEvaluating || !textInput.trim()}
                    className={`w-full py-3 px-4 rounded-lg font-medium ${
                      isEvaluating || !textInput.trim()
                        ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                        : isDarkMode
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-blue-500 hover:bg-blue-600 text-white'
                    }`}>
                    {isEvaluating ? (
                      <span className="flex items-center justify-center">
                        <AiOutlineLoading3Quarters className="animate-spin mr-2" />
                        Evaluating...
                      </span>
                    ) : (
                      'Evaluate Document'
                    )}
                  </button>
                </div>
              )}
            </>
          )}

          {/* Evaluation Results */}
          {evaluationResult && (
            <div className="space-y-6">
              {evaluationResult.success ? (
                <>
                  {/* Overall Score */}
                  <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold">Overall Evaluation Score</h3>
                      {evaluationResult.filename && (
                        <span className="text-sm opacity-75">File: {evaluationResult.filename}</span>
                      )}
                    </div>
                    <div className="flex items-center">
                      <span
                        className={`text-4xl font-bold ${
                          evaluationResult.overall_score
                            ? getOverallScoreColor(evaluationResult.overall_score)
                            : 'text-gray-400'
                        }`}>
                        {evaluationResult.overall_score ? formatScore(evaluationResult.overall_score) : 'N/A'}
                      </span>
                      {!isQualitativeScore(evaluationResult.overall_score || 0) && (
                        <span className="text-2xl ml-2 opacity-75">/ 100</span>
                      )}
                      <FiCheckCircle
                        className={`ml-4 ${
                          evaluationResult.overall_score && getScoreThreshold(evaluationResult.overall_score, 70)
                            ? 'text-green-500'
                            : 'text-yellow-500'
                        }`}
                        size={24}
                      />
                    </div>
                  </div>

                  {/* Flow Scores */}
                  {evaluationResult.flow_scores && evaluationResult.flow_scores.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Individual Flow Scores</h3>
                      <div className="space-y-4">
                        {evaluationResult.flow_scores.map((flow, index) => (
                          <div
                            key={index}
                            className={`p-4 rounded-lg border ${
                              isDarkMode ? 'border-gray-600 bg-gray-750' : 'border-gray-200 bg-gray-50'
                            }`}>
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium text-lg">{flow.flow_name}</h4>
                              <span className={`text-xl font-semibold ${getOverallScoreColor(flow.score)}`}>
                                {formatScore(flow.score)}
                              </span>
                            </div>

                            {/* Detailed Scores */}
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                              <div>
                                <span className="text-sm opacity-75">UI Element Mapping</span>
                                <div className={`font-medium ${getScoreColor(flow.ui_element_mapping, 30)}`}>
                                  {formatScore(flow.ui_element_mapping, 30)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Flow Coverage</span>
                                <div className={`font-medium ${getScoreColor(flow.flow_coverage, 30)}`}>
                                  {formatScore(flow.flow_coverage, 30)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Action-Intent Mapping</span>
                                <div className={`font-medium ${getScoreColor(flow.action_intent_mapping, 20)}`}>
                                  {formatScore(flow.action_intent_mapping, 20)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Structure</span>
                                <div className={`font-medium ${getScoreColor(flow.structure, 15)}`}>
                                  {formatScore(flow.structure, 15)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Ambiguity/Noise</span>
                                <div className={`font-medium ${getScoreColor(flow.ambiguity_noise, 5)}`}>
                                  {formatScore(flow.ambiguity_noise, 5)}
                                </div>
                              </div>
                            </div>

                            {/* Recommendations */}
                            {flow.recommendations && flow.recommendations.length > 0 && (
                              <div>
                                <h5 className="font-medium mb-2 text-sm opacity-75">Recommendations:</h5>
                                <ul className="space-y-1 text-sm">
                                  {flow.recommendations.map((rec, recIndex) => (
                                    <li key={recIndex} className="flex items-start">
                                      <span className="text-blue-500 mr-2">•</span>
                                      <span>{rec}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Global Recommendations */}
                  {evaluationResult.global_recommendations && evaluationResult.global_recommendations.length > 0 && (
                    <div
                      className={`p-4 rounded-lg ${
                        isDarkMode ? 'bg-blue-900 bg-opacity-20 border-blue-600' : 'bg-blue-50 border-blue-200'
                      } border`}>
                      <h3 className="text-lg font-semibold mb-3 flex items-center">
                        <FiAlertCircle className="mr-2 text-blue-500" />
                        Global Recommendations
                      </h3>
                      <ul className="space-y-2">
                        {evaluationResult.global_recommendations.map((rec, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </>
              ) : (
                <div
                  className={`p-6 rounded-lg border ${
                    isDarkMode ? 'border-red-600 bg-red-900 bg-opacity-20' : 'border-red-200 bg-red-50'
                  }`}>
                  <div className="flex items-center mb-2">
                    <FiAlertCircle className="text-red-500 mr-2" size={20} />
                    <h3 className="font-semibold text-red-600">Evaluation Failed</h3>
                  </div>
                  <p className="text-red-600">
                    {evaluationResult.error || 'An unknown error occurred during evaluation.'}
                  </p>
                </div>
              )}

              {/* New Evaluation Button */}
              <button
                onClick={() => {
                  setEvaluationResult(null);
                  setTextInput('');
                }}
                className={`w-full py-2 px-4 rounded-lg border ${
                  isDarkMode
                    ? 'border-gray-600 hover:bg-gray-700 text-gray-300'
                    : 'border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}>
                Evaluate Another Document
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentEvaluationModal;
