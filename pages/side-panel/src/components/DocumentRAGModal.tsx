import React, { useState } from 'react';
import { FiUpload, FiSearch, FiFile, FiFolder, FiX } from 'react-icons/fi';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { uploadFileWithContext, queryProjectDocuments, queryFileDocuments, QueryResult } from '../services/vectordb';

interface DocumentRAGModalProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
}

interface UploadedFile {
  file_id: string;
  filename: string;
  file_url: string;
  chunks_added: number;
}

const DocumentRAGModal: React.FC<DocumentRAGModalProps> = ({ isOpen, onClose, isDarkMode }) => {
  const [activeTab, setActiveTab] = useState<'upload' | 'query'>('upload');
  const [isUploading, setIsUploading] = useState(false);
  const [isQuerying, setIsQuerying] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [queryText, setQueryText] = useState('');
  const [queryResults, setQueryResults] = useState<QueryResult[]>([]);
  const [selectedFileId, setSelectedFileId] = useState<string>('');
  const [queryMode, setQueryMode] = useState<'project' | 'file'>('project');
  const [dragActive, setDragActive] = useState(false);

  // Demo values - in real implementation these would come from auth/session
  const userId = 'demo_user';
  const projectId = 'demo_project';

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  const handleFileUpload = async (file: File) => {
    const allowedTypes = [
      'text/plain',
      'text/markdown',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    const allowedExtensions = ['.txt', '.md', '.pdf', '.docx'];

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      alert('Please upload a .txt, .md, .pdf, or .docx file');
      return;
    }

    setIsUploading(true);
    try {
      const result = await uploadFileWithContext(file, userId, projectId);

      const newFile: UploadedFile = {
        file_id: result.file_id,
        filename: result.filename,
        file_url: result.file_url,
        chunks_added: result.chunks_added,
      };

      setUploadedFiles(prev => [...prev, newFile]);
      alert(`File uploaded successfully! ${result.chunks_added} chunks indexed.`);
    } catch (error) {
      console.error('Upload error:', error);
      alert(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleQuery = async () => {
    if (!queryText.trim()) {
      alert('Please enter a search query');
      return;
    }

    if (queryMode === 'file' && !selectedFileId) {
      alert('Please select a file to search within');
      return;
    }

    setIsQuerying(true);
    try {
      let results;
      if (queryMode === 'project') {
        const response = await queryProjectDocuments({
          query: queryText,
          user_id: userId,
          project_id: projectId,
          top_k: 5,
        });
        results = response.results;
      } else {
        const response = await queryFileDocuments({
          query: queryText,
          file_id: selectedFileId,
          user_id: userId,
          project_id: projectId,
          top_k: 5,
        });
        results = response.results;
      }

      setQueryResults(results);
    } catch (error) {
      console.error('Query error:', error);
      alert(`Query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsQuerying(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className={`w-full max-w-4xl h-5/6 rounded-lg shadow-xl ${
          isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
        }`}>
        {/* Header */}
        <div
          className={`flex items-center justify-between p-4 border-b ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
          <h2 className="text-xl font-semibold">Document Upload & RAG</h2>
          <button
            onClick={onClose}
            className={`p-2 rounded-lg hover:bg-opacity-80 ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>
            <FiX size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className={`flex border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <button
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'upload'
                ? isDarkMode
                  ? 'border-b-2 border-blue-400 text-blue-400'
                  : 'border-b-2 border-blue-500 text-blue-600'
                : isDarkMode
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-gray-600 hover:text-gray-800'
            }`}>
            <FiUpload className="inline mr-2" />
            Upload Documents
          </button>
          <button
            onClick={() => setActiveTab('query')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'query'
                ? isDarkMode
                  ? 'border-b-2 border-blue-400 text-blue-400'
                  : 'border-b-2 border-blue-500 text-blue-600'
                : isDarkMode
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-gray-600 hover:text-gray-800'
            }`}>
            <FiSearch className="inline mr-2" />
            Query Documents
          </button>
        </div>

        {/* Content */}
        <div className="p-4 h-full overflow-y-auto">
          {activeTab === 'upload' && (
            <div className="space-y-4">
              {/* Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center ${
                  dragActive
                    ? isDarkMode
                      ? 'border-blue-400 bg-blue-900 bg-opacity-20'
                      : 'border-blue-400 bg-blue-50'
                    : isDarkMode
                      ? 'border-gray-600 hover:border-gray-500'
                      : 'border-gray-300 hover:border-gray-400'
                } ${isUploading ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => document.getElementById('file-input')?.click()}>
                <input
                  id="file-input"
                  type="file"
                  accept=".txt,.md,.pdf,.docx"
                  onChange={handleFileInput}
                  className="hidden"
                  disabled={isUploading}
                />

                {isUploading ? (
                  <div className="flex flex-col items-center">
                    <AiOutlineLoading3Quarters className="animate-spin mb-4" size={48} />
                    <p className="text-lg font-medium">Uploading and Processing...</p>
                    <p className="text-sm opacity-75 mt-2">This may take a few moments</p>
                  </div>
                ) : (
                  <div>
                    <FiUpload className="mx-auto mb-4" size={48} />
                    <p className="text-lg font-medium mb-2">
                      {dragActive ? 'Drop your document here' : 'Upload a document'}
                    </p>
                    <p className="text-sm opacity-75">Supported formats: .txt, .md, .pdf, .docx</p>
                  </div>
                )}
              </div>

              {/* Uploaded Files List */}
              {uploadedFiles.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Uploaded Files</h3>
                  <div className="space-y-2">
                    {uploadedFiles.map(file => (
                      <div
                        key={file.file_id}
                        className={`p-3 rounded-lg border ${
                          isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                        }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FiFile className="mr-2" />
                            <span className="font-medium">{file.filename}</span>
                          </div>
                          <span className="text-sm opacity-75">{file.chunks_added} chunks</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'query' && (
            <div className="space-y-4">
              {/* Query Mode Selection */}
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="project"
                    checked={queryMode === 'project'}
                    onChange={e => setQueryMode(e.target.value as 'project' | 'file')}
                    className="mr-2"
                  />
                  <FiFolder className="mr-1" />
                  Search entire project
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="file"
                    checked={queryMode === 'file'}
                    onChange={e => setQueryMode(e.target.value as 'project' | 'file')}
                    className="mr-2"
                  />
                  <FiFile className="mr-1" />
                  Search specific file
                </label>
              </div>

              {/* File Selection (when file mode is selected) */}
              {queryMode === 'file' && (
                <div>
                  <label className="block text-sm font-medium mb-2">Select File:</label>
                  <select
                    value={selectedFileId}
                    onChange={e => setSelectedFileId(e.target.value)}
                    className={`w-full p-2 border rounded-lg ${
                      isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}>
                    <option value="">Select a file...</option>
                    {uploadedFiles.map(file => (
                      <option key={file.file_id} value={file.file_id}>
                        {file.filename}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Query Input */}
              <div>
                <label className="block text-sm font-medium mb-2">Search Query:</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={queryText}
                    onChange={e => setQueryText(e.target.value)}
                    placeholder="Enter your search query..."
                    className={`flex-1 p-2 border rounded-lg ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    onKeyPress={e => e.key === 'Enter' && handleQuery()}
                  />
                  <button
                    onClick={handleQuery}
                    disabled={isQuerying}
                    className={`px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 ${
                      isQuerying ? 'cursor-not-allowed' : ''
                    }`}>
                    {isQuerying ? (
                      <AiOutlineLoading3Quarters className="animate-spin" size={20} />
                    ) : (
                      <FiSearch size={20} />
                    )}
                  </button>
                </div>
              </div>

              {/* Query Results */}
              {queryResults.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Search Results</h3>
                  <div className="space-y-3">
                    {queryResults.map((result, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border ${
                          isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                        }`}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{result.filename}</span>
                          <span className="text-xs opacity-75">
                            Score: {result.score.toFixed(3)} | Chunk: {result.chunk_index}
                          </span>
                        </div>
                        <p className="text-sm">{result.text}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentRAGModal;
