import React, { useState, useEffect } from 'react';

interface PlaywrightScriptOptions {
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
  includeComments: boolean;
  includeAssertions: boolean;
  includeWaitFor: boolean;
}

interface SessionInfo {
  sessionId: string | null;
  actionCount: number;
}

interface LanguageOption {
  value: string;
  label: string;
}

export const PlaywrightGenerator: React.FC = () => {
  const [scriptOptions, setScriptOptions] = useState<PlaywrightScriptOptions>({
    language: 'javascript',
    includeComments: true,
    includeAssertions: true,
    includeWaitFor: true,
  });

  const [sessionInfo, setSessionInfo] = useState<SessionInfo>({
    sessionId: null,
    actionCount: 0,
  });

  const [availableLanguages, setAvailableLanguages] = useState<LanguageOption[]>([
    { value: 'javascript', label: 'JavaScript' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'csharp', label: 'C#' },
  ]);
  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadActionTrackerInfo();
  }, []);

  const loadActionTrackerInfo = async () => {
    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({ type: 'get_action_tracker_info' });

      port.onMessage.addListener(message => {
        if (message.type === 'action_tracker_info') {
          setSessionInfo(message.sessionInfo);
          setAvailableLanguages(message.availableLanguages);
        } else if (message.type === 'error') {
          setError(message.error);
        }
      });
    } catch (error) {
      setError('Failed to load action tracker info');
    }
  };

  const generateScript = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({
        type: 'generate_playwright_script',
        options: scriptOptions,
      });

      port.onMessage.addListener(message => {
        if (message.type === 'playwright_script_generated') {
          setGeneratedScript(message.script);
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to generate Playwright script');
      setIsLoading(false);
    }
  };

  const exportScript = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({
        type: 'export_playwright_script',
        options: {
          ...scriptOptions,
          filename: `playwright-test-${Date.now()}.${getFileExtension(scriptOptions.language)}`,
        },
      });

      port.onMessage.addListener(message => {
        if (message.type === 'playwright_script_exported') {
          // Script was exported successfully
          setError(null);
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to export Playwright script');
      setIsLoading(false);
    }
  };

  const resetTracker = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({ type: 'reset_action_tracker' });

      port.onMessage.addListener(message => {
        if (message.type === 'action_tracker_reset') {
          loadActionTrackerInfo(); // Reload info after reset
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to reset action tracker');
      setIsLoading(false);
    }
  };

  const reRunActions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({
        type: 're_run_actions',
      });

      port.onMessage.addListener(message => {
        if (message.type === 'actions_re_run_complete') {
          setError(null);
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to re-run actions');
      setIsLoading(false);
    }
  };

  const getFileExtension = (language: string): string => {
    switch (language) {
      case 'typescript':
        return 'ts';
      case 'python':
        return 'py';
      case 'java':
        return 'java';
      case 'csharp':
        return 'cs';
      default:
        return 'js';
    }
  };

  return (
    <div className="space-y-4 p-4">
      <div className="border-b pb-4">
        <h2 className="text-lg font-semibold text-gray-900">Playwright Script Generator</h2>
        <p className="mt-1 text-sm text-gray-600">Generate Playwright test scripts from tracked browser actions</p>
      </div>

      {/* Session Info */}
      <div className="rounded-lg bg-gray-50 p-3">
        <h3 className="mb-2 text-sm font-medium text-gray-700">Session Information</h3>
        <div className="text-sm text-gray-600">
          <p>Session ID: {sessionInfo.sessionId || 'None'}</p>
          <p>Tracked Actions: {sessionInfo.actionCount}</p>
        </div>
      </div>

      {/* Script Options */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-gray-700">Script Options</h3>

        <div>
          <label htmlFor="language-select" className="mb-1 block text-sm font-medium text-gray-700">
            Language
          </label>
          <select
            id="language-select"
            value={scriptOptions.language}
            onChange={e =>
              setScriptOptions({
                ...scriptOptions,
                language: e.target.value as PlaywrightScriptOptions['language'],
              })
            }
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            {availableLanguages.map(lang => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={scriptOptions.includeComments}
              onChange={e =>
                setScriptOptions({
                  ...scriptOptions,
                  includeComments: e.target.checked,
                })
              }
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Include comments</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={scriptOptions.includeAssertions}
              onChange={e =>
                setScriptOptions({
                  ...scriptOptions,
                  includeAssertions: e.target.checked,
                })
              }
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Include assertions</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={scriptOptions.includeWaitFor}
              onChange={e =>
                setScriptOptions({
                  ...scriptOptions,
                  includeWaitFor: e.target.checked,
                })
              }
              className="mr-2"
            />
            <span className="text-sm text-gray-700">
              Include wait between actions (and page load waits after navigation)
            </span>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <button
          onClick={generateScript}
          disabled={isLoading || sessionInfo.actionCount === 0}
          className={`flex-1 rounded-md px-4 py-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            isLoading || sessionInfo.actionCount === 0
              ? 'cursor-not-allowed bg-gray-300 text-gray-500'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}>
          {isLoading ? 'Generating...' : 'Generate Script'}
        </button>

        <button
          onClick={exportScript}
          disabled={isLoading || sessionInfo.actionCount === 0}
          className={`flex-1 rounded-md px-4 py-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            isLoading || sessionInfo.actionCount === 0
              ? 'cursor-not-allowed bg-gray-300 text-gray-500'
              : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
          }`}>
          {isLoading ? 'Exporting...' : 'Export Script'}
        </button>

        <button
          onClick={reRunActions}
          disabled={isLoading || sessionInfo.actionCount === 0}
          className={`flex-1 rounded-md px-4 py-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            isLoading || sessionInfo.actionCount === 0
              ? 'cursor-not-allowed bg-gray-300 text-gray-500'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}>
          {isLoading ? 'Re-running...' : 'Re-run Actions'}
        </button>

        <button
          onClick={resetTracker}
          disabled={isLoading}
          className={`flex-1 rounded-md px-4 py-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            isLoading ? 'cursor-not-allowed bg-gray-300 text-gray-500' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
          }`}>
          Reset Tracker
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="rounded-md border border-red-200 bg-red-50 p-3">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Generated Script Preview */}
      {generatedScript && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-700">Generated Script Preview</h3>
            <button
              onClick={() => {
                navigator.clipboard.writeText(generatedScript);
              }}
              className="rounded-md bg-blue-600 px-3 py-1 text-sm text-white transition-colors duration-200 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              Copy to Clipboard
            </button>
          </div>
          <div className="overflow-x-auto rounded-md bg-gray-900 p-4 text-gray-100">
            <pre className="whitespace-pre-wrap text-xs">{generatedScript}</pre>
          </div>
          <div className="flex justify-end">
            <button
              onClick={() => {
                navigator.clipboard.writeText(generatedScript);
              }}
              className="rounded-md bg-gray-200 px-3 py-1 text-gray-800 transition-colors duration-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              Copy to Clipboard
            </button>
          </div>
        </div>
      )}

      {/* No Actions Message */}
      {sessionInfo.actionCount === 0 && (
        <div className="rounded-md border border-yellow-200 bg-yellow-50 p-3">
          <p className="text-sm text-yellow-700">
            No actions have been tracked yet. Start performing actions in the browser to generate a Playwright script.
          </p>
        </div>
      )}

      {/* Info about executable actions */}
      {sessionInfo.actionCount > 0 && (
        <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
          <p className="text-sm text-blue-700">
            <strong>Re-run Actions:</strong> Only browser interactions (clicks, navigation, input, etc.) will be
            executed. AI completion messages and other non-executable actions will be skipped.
          </p>
        </div>
      )}
    </div>
  );
};
