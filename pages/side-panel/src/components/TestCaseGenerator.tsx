/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  FiPlay,
  FiRefreshCw,
  FiCheckCircle,
  FiX,
  FiClock,
  FiAlertCircle,
  FiChevronDown,
  FiChevronRight,
  FiSquare,
} from 'react-icons/fi';
import { testCaseService } from '../services/testcase';
import type { TestCase } from '../types/testcase';

interface TestCaseGeneratorProps {
  userPrompt: string;
  onTestCasesGenerated?: (testCases: TestCase[]) => void;
  onExecutionComplete?: (results: TestCaseResult[]) => void;
  onTestCaseExecution?: (testCase: TestCase) => Promise<string[]>;
  isDarkMode?: boolean;
}

interface TestCaseResult {
  id: string;
  name: string;
  status: string;
  execution_time?: number;
  error_message?: string;
  validator_results?: string[]; // Results from validator steps
}

export default function TestCaseGenerator({
  userPrompt,
  onTestCasesGenerated,
  onExecutionComplete,
  onTestCaseExecution,
  isDarkMode = false,
}: TestCaseGeneratorProps) {
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [currentlyExecuting, setCurrentlyExecuting] = useState<string | null>(null);
  const [executionResults, setExecutionResults] = useState<TestCaseResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [expandedTestCases, setExpandedTestCases] = useState<Set<string>>(new Set());
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [stopRequested, setStopRequested] = useState(false);
  const shouldStopExecutionRef = useRef(false);

  // Generate test cases when component mounts
  useEffect(() => {
    if (userPrompt.trim()) {
      generateTestCases();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userPrompt]);

  const generateTestCases = useCallback(async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const request = {
        user_prompt: userPrompt,
        generate_count: 5,
      };

      const generatedTestCases = await testCaseService.generateTestCases(request);

      // Add selected property for UI state
      const testCasesWithSelection = generatedTestCases.map(tc => ({
        ...tc,
        selected: true, // Default to selected
      }));

      setTestCases(testCasesWithSelection);
      onTestCasesGenerated?.(generatedTestCases);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate test cases';
      setError(errorMessage);
      console.error('Error generating test cases:', err);
    } finally {
      setIsGenerating(false);
    }
  }, [userPrompt, onTestCasesGenerated]);

  const toggleTestCaseSelection = useCallback((testId: string) => {
    setTestCases(prev => prev.map(tc => (tc.id === testId ? { ...tc, selected: !tc.selected } : tc)));
  }, []);

  const toggleSelectAll = useCallback(() => {
    const allSelected = testCases.every(tc => tc.selected);
    setTestCases(prev => prev.map(tc => ({ ...tc, selected: !allSelected })));
  }, [testCases]);

  const toggleTestCaseExpansion = useCallback((testId: string) => {
    setExpandedTestCases(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(testId)) {
        newExpanded.delete(testId);
      } else {
        newExpanded.add(testId);
      }
      return newExpanded;
    });
  }, []);

  const executeSelectedTestCases = useCallback(async () => {
    if (!onTestCaseExecution) {
      setError('Test case execution handler not available');
      return;
    }

    const selectedTestCases = testCases.filter(tc => tc.selected);
    if (selectedTestCases.length === 0) {
      setError('Please select at least one test case to execute');
      return;
    }

    setIsExecuting(true);
    setStopRequested(false);
    shouldStopExecutionRef.current = false;
    setIsCollapsed(true); // Collapse the component when execution starts
    setError(null);
    const results: TestCaseResult[] = [];

    try {
      // Execute test cases sequentially through agent workflow
      for (const testCase of selectedTestCases) {
        // Check if execution should be stopped
        if (shouldStopExecutionRef.current) {
          results.push({
            id: testCase.id,
            name: testCase.name,
            status: 'stopped',
            error_message: 'Execution stopped by user',
          });
          continue;
        }

        setCurrentlyExecuting(testCase.id);

        const startTime = Date.now();

        try {
          // Update status to running
          try {
            await testCaseService.updateTestCaseStatus({
              test_id: testCase.id,
              status: 'running',
            });
          } catch (statusError) {
            console.warn('Failed to update test status to running:', statusError);
          }

          // Check again before starting execution in case stop was pressed
          if (shouldStopExecutionRef.current) {
            results.push({
              id: testCase.id,
              name: testCase.name,
              status: 'stopped',
              error_message: 'Execution stopped by user',
            });
            break;
          }

          // Execute the test case through the agent workflow and capture validator results
          const validatorResults = await onTestCaseExecution(testCase);

          // Check if stop was pressed during execution
          if (shouldStopExecutionRef.current) {
            results.push({
              id: testCase.id,
              name: testCase.name,
              status: 'stopped',
              error_message: 'Execution stopped by user',
            });
            break;
          }

          const executionTime = (Date.now() - startTime) / 1000;

          // Update status to passed
          try {
            await testCaseService.updateTestCaseStatus({
              test_id: testCase.id,
              status: 'passed',
              execution_time: executionTime,
            });
          } catch (statusError) {
            console.warn('Failed to update test status to passed:', statusError);
          }

          results.push({
            id: testCase.id,
            name: testCase.name,
            status: 'passed',
            execution_time: executionTime,
            validator_results: validatorResults,
          });
        } catch (err) {
          const executionTime = (Date.now() - startTime) / 1000;
          const errorMessage = err instanceof Error ? err.message : 'Execution failed';

          // Update status to failed/error
          try {
            await testCaseService.updateTestCaseStatus({
              test_id: testCase.id,
              status: 'failed',
              execution_time: executionTime,
              error_message: errorMessage,
            });
          } catch (statusError) {
            console.warn('Failed to update test status to failed:', statusError);
          }

          results.push({
            id: testCase.id,
            name: testCase.name,
            status: 'failed',
            error_message: errorMessage,
          });
        }

        // Small delay between test cases to allow stop button to be responsive
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setExecutionResults(results);
      onExecutionComplete?.(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to execute test cases';
      setError(errorMessage);
    } finally {
      setIsExecuting(false);
      setStopRequested(false);
      shouldStopExecutionRef.current = false;
      setCurrentlyExecuting(null);
    }
  }, [testCases, onExecutionComplete, onTestCaseExecution]);

  const stopExecution = useCallback(() => {
    shouldStopExecutionRef.current = true;
    setStopRequested(true);
  }, []);

  // Utility function to manually update test case status
  // This can be used externally to update test case status outside of execution flow
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const updateTestCaseStatus = useCallback(
    async (
      testId: string,
      status: 'pending' | 'running' | 'passed' | 'failed' | 'error' | 'skipped',
      options?: {
        execution_time?: number;
        error_message?: string;
        screenshot_path?: string;
      },
    ) => {
      try {
        await testCaseService.updateTestCaseStatus({
          test_id: testId,
          status,
          ...options,
        });
        console.log(`Test case ${testId} status updated to ${status}`);
      } catch (error) {
        console.error(`Failed to update test case ${testId} status:`, error);
        throw error;
      }
    },
    [],
  );

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'passed':
        return <FiCheckCircle className="text-green-500" />;
      case 'failed':
        return <FiX className="text-red-500" />;
      case 'running':
        return <FiRefreshCw className="animate-spin text-blue-500" />;
      case 'error':
        return <FiAlertCircle className="text-red-500" />;
      default:
        return <FiClock className={isDarkMode ? 'text-[#875bf8]' : 'text-sky-500'} />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return isDarkMode ? 'bg-[#0d0d22]/60 text-white border-[#2e2e60]' : 'bg-sky-50 text-sky-800 border-sky-200';
    }
  };

  if (isGenerating) {
    return (
      <div
        className={`rounded-lg p-4 ${isDarkMode ? 'border border-[#2e2e60] bg-[#0d0d22]/60' : 'border border-sky-200 bg-sky-50'}`}>
        <div className="flex items-center gap-3">
          <FiRefreshCw className="animate-spin text-blue-500" />
          <span className={isDarkMode ? 'text-white' : 'text-sky-700'}>Generating test cases...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`rounded-lg border p-4 ${isDarkMode ? 'border-red-600 bg-red-900/20 text-red-400' : 'border-red-200 bg-red-50 text-red-700'}`}>
        <div className="flex items-center gap-2">
          <FiAlertCircle />
          <span>Error: {error}</span>
        </div>
        <button
          onClick={generateTestCases}
          className={`mt-2 rounded px-3 py-1 text-sm ${
            isDarkMode ? 'bg-red-700 text-white hover:bg-red-600' : 'bg-red-100 text-red-800 hover:bg-red-200'
          }`}>
          Retry
        </button>
      </div>
    );
  }

  if (testCases.length === 0) {
    return null;
  }

  const selectedCount = testCases.filter(tc => tc.selected).length;

  // Show collapsed view when executing
  if (isCollapsed && isExecuting) {
    return (
      <div
        className={`rounded-lg p-4 ${isDarkMode ? 'border border-[#2e2e60] bg-[#0d0d22]/60' : 'border border-sky-200 bg-sky-50'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FiRefreshCw className="animate-spin text-blue-500" />
            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>
              Executing Test Cases ({selectedCount} selected)
            </span>
          </div>
          <button
            onClick={() => setIsCollapsed(false)}
            className={`rounded px-3 py-1 text-sm hover:bg-opacity-80 ${
              isDarkMode ? 'bg-[#2e2e60] text-white hover:bg-[#875bf8]' : 'bg-sky-200 text-sky-700 hover:bg-sky-300'
            }`}>
            Show Details
          </button>
        </div>
        {currentlyExecuting && (
          <div className={`mt-2 text-sm ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
            Currently executing: {testCases.find(tc => tc.id === currentlyExecuting)?.name}
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={`space-y-4 rounded-lg p-4 ${isDarkMode ? 'border border-[#2e2e60] bg-[#0d0d22]/60' : 'border border-sky-200 bg-sky-50'}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>
          Generated Test Cases ({testCases.length})
        </h3>
        <div className="flex items-center gap-2">
          <label className="flex cursor-pointer items-center gap-2">
            <input
              type="checkbox"
              checked={testCases.every(tc => tc.selected)}
              onChange={toggleSelectAll}
              className={`rounded ${
                isDarkMode
                  ? 'border-[#875bf8] bg-[#2e2e60] text-[#875bf8] focus:ring-[#875bf8]'
                  : 'border-sky-300 bg-white text-sky-600 focus:ring-sky-500'
              }`}
            />
            <span className={`text-sm ${isDarkMode ? 'text-white' : 'text-sky-600'}`}>Select All</span>
          </label>
          <span className={`text-sm ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-500'}`}>
            ({selectedCount} selected)
          </span>
        </div>
      </div>

      {/* Test Cases List */}
      <div
        className={`scrollbar-thin max-h-96 space-y-3 overflow-y-auto ${
          isDarkMode ? 'scrollbar-track-dark' : 'scrollbar-track-light'
        }`}>
        {testCases.map(testCase => (
          <div
            key={testCase.id}
            className={`rounded-lg border p-3 ${
              isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]/40' : 'border-sky-200 bg-white'
            } ${
              testCase.selected
                ? isDarkMode
                  ? 'ring-2 ring-[#875bf8] ring-opacity-50'
                  : 'ring-2 ring-sky-500 ring-opacity-50'
                : ''
            }`}>
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                checked={testCase.selected}
                onChange={() => toggleTestCaseSelection(testCase.id)}
                className={`mt-1 rounded ${
                  isDarkMode
                    ? 'border-[#875bf8] bg-[#2e2e60] text-[#875bf8] focus:ring-[#875bf8]'
                    : 'border-sky-300 bg-white text-sky-600 focus:ring-sky-500'
                }`}
                disabled={isExecuting}
              />

              <div className="min-w-0 flex-1">
                <div className="mb-2 flex items-center gap-2">
                  <h4 className={`truncate font-medium ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>
                    {testCase.name}
                  </h4>
                  {currentlyExecuting === testCase.id && (
                    <FiRefreshCw className="shrink-0 animate-spin text-blue-500" />
                  )}
                  <div className="flex shrink-0 items-center gap-1">
                    {getStatusIcon(testCase.status)}
                    <span className={`rounded border px-2 py-1 text-xs ${getSeverityColor(testCase.severity)}`}>
                      {testCase.severity}
                    </span>
                  </div>
                </div>

                <p className={`mb-2 text-sm ${isDarkMode ? 'text-white' : 'text-sky-600'}`}>{testCase.description}</p>

                {/* Test Case Details Section */}
                <div className="mb-2 text-xs">
                  <button
                    onClick={() => toggleTestCaseExpansion(testCase.id)}
                    className={`flex items-center gap-1 font-medium hover:underline ${
                      isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-600 hover:text-sky-700'
                    }`}
                    disabled={isExecuting}>
                    {expandedTestCases.has(testCase.id) ? (
                      <FiChevronDown className="size-3" />
                    ) : (
                      <FiChevronRight className="size-3" />
                    )}
                    Details
                  </button>

                  {expandedTestCases.has(testCase.id) && (
                    <div className={`mt-2 border-l-2 pl-4 ${isDarkMode ? 'border-[#2e2e60]' : 'border-sky-300'}`}>
                      <div className="space-y-2">
                        <div
                          className={`rounded p-2 text-xs ${
                            isDarkMode ? 'bg-[#0d0d22]/60 text-white' : 'bg-sky-50 text-sky-700'
                          }`}>
                          <span className={`font-medium ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
                            Expected Result:
                          </span>{' '}
                          {testCase.expected_result}
                        </div>
                        <div
                          className={`rounded p-2 text-xs ${
                            isDarkMode ? 'bg-[#0d0d22]/60 text-white' : 'bg-sky-50 text-sky-700'
                          }`}>
                          <span className={`font-medium ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
                            Breaking Scenario:
                          </span>{' '}
                          {testCase.breaking_scenario}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Execution Controls */}
      <div
        className={`flex items-center justify-between border-t pt-3 ${isDarkMode ? 'border-[#2e2e60]' : 'border-sky-300'}`}>
        <div className="flex items-center gap-3">
          <button
            onClick={executeSelectedTestCases}
            disabled={selectedCount === 0 || isExecuting}
            className={`flex items-center gap-2 rounded-lg px-4 py-2 font-medium ${
              selectedCount > 0 && !isExecuting
                ? isDarkMode
                  ? 'bg-[#875bf8] text-white hover:bg-[#a478f9]'
                  : 'bg-sky-600 text-white hover:bg-sky-700'
                : isDarkMode
                  ? 'cursor-not-allowed bg-[#2e2e60] text-[#875bf8]'
                  : 'cursor-not-allowed bg-sky-200 text-sky-400'
            }`}>
            {isExecuting ? (
              <>
                <FiRefreshCw className="animate-spin" />
                Executing Tests...
              </>
            ) : (
              <>
                <FiPlay />
                Execute Selected Tests ({selectedCount})
              </>
            )}
          </button>

          {isExecuting && (
            <button
              onClick={stopExecution}
              className={`flex items-center gap-2 rounded-lg px-4 py-2 font-medium ${
                stopRequested
                  ? isDarkMode
                    ? 'cursor-not-allowed bg-gray-600 text-gray-400'
                    : 'cursor-not-allowed bg-gray-300 text-gray-500'
                  : isDarkMode
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-red-500 text-white hover:bg-red-600'
              }`}
              disabled={stopRequested}>
              <FiSquare />
              {stopRequested ? 'Stopping...' : 'Stop Execution'}
            </button>
          )}
        </div>

        {executionResults.length > 0 && (
          <div className={`text-sm ${isDarkMode ? 'text-white' : 'text-sky-600'}`}>
            Completed: {executionResults.filter(r => r.status === 'passed').length} passed,{' '}
            {executionResults.filter(r => r.status === 'failed').length} failed
          </div>
        )}
      </div>
    </div>
  );
}
