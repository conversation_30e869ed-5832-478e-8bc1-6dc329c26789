/* eslint-disable tailwindcss/enforces-shorthand */
/* eslint-disable tailwindcss/classnames-order */
import React, { useState } from 'react';
import { FiCheckCircle, FiX, FiAlertCircle, FiClock, FiChevronDown, FiChevronRight } from 'react-icons/fi';

interface TestCaseResult {
  id: string;
  name: string;
  status: string;
  execution_time?: number;
  error_message?: string;
  validator_results?: string[]; // Results from validator steps
}

interface TestCaseResultsSummaryProps {
  results: TestCaseResult[];
  isDarkMode?: boolean;
}

export default function TestCaseResultsSummary({ results, isDarkMode = false }: TestCaseResultsSummaryProps) {
  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set());

  const toggleResultExpansion = (resultId: string) => {
    setExpandedResults(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(resultId)) {
        newExpanded.delete(resultId);
      } else {
        newExpanded.add(resultId);
      }
      return newExpanded;
    });
  };

  if (results.length === 0) {
    return null;
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'passed':
        return <FiCheckCircle className="text-green-500" />;
      case 'failed':
        return <FiX className="text-red-500" />;
      case 'error':
        return <FiAlertCircle className="text-red-500" />;
      case 'stopped':
        return <FiAlertCircle className="text-yellow-500" />;
      default:
        return <FiClock className={isDarkMode ? 'text-[#875bf8]' : 'text-sky-500'} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'passed':
        return isDarkMode
          ? 'bg-green-900/20 border-green-600 text-green-400'
          : 'bg-green-50 border-green-200 text-green-700';
      case 'failed':
        return isDarkMode ? 'bg-red-900/20 border-red-600 text-red-400' : 'bg-red-50 border-red-200 text-red-700';
      case 'error':
        return isDarkMode ? 'bg-red-900/20 border-red-600 text-red-400' : 'bg-red-50 border-red-200 text-red-700';
      case 'stopped':
        return isDarkMode
          ? 'bg-yellow-900/20 border-yellow-600 text-yellow-400'
          : 'bg-yellow-50 border-yellow-200 text-yellow-700';
      default:
        return isDarkMode ? 'bg-[#0d0d22]/80 border-[#2e2e60] text-white' : 'bg-sky-50 border-sky-200 text-sky-700';
    }
  };

  const passedCount = results.filter(r => r.status === 'passed').length;
  const failedCount = results.filter(r => r.status === 'failed').length;
  const errorCount = results.filter(r => r.status === 'error').length;
  const stoppedCount = results.filter(r => r.status === 'stopped').length;
  const totalTime = results.reduce((sum, r) => sum + (r.execution_time || 0), 0);

  return (
    <div
      className={`p-4 rounded-lg ${isDarkMode ? 'bg-[#0d0d22]/80 border border-[#2e2e60]' : 'bg-sky-50 border border-sky-200'}`}>
      <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>
        Test Execution Summary
      </h3>

      {/* Overall Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div
          className={`p-3 rounded-lg border ${isDarkMode ? 'bg-[#0d0d22]/60 border-[#2e2e60]' : 'bg-white border-sky-200'}`}>
          <div className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>{results.length}</div>
          <div className={`text-sm ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>Total Tests</div>
        </div>

        <div
          className={`p-3 rounded-lg border ${isDarkMode ? 'bg-green-900/20 border-green-600' : 'bg-green-50 border-green-200'}`}>
          <div className="text-2xl font-bold text-green-500">{passedCount}</div>
          <div className={`text-sm ${isDarkMode ? 'text-green-400' : 'text-green-700'}`}>Passed</div>
        </div>

        <div
          className={`p-3 rounded-lg border ${isDarkMode ? 'bg-red-900/20 border-red-600' : 'bg-red-50 border-red-200'}`}>
          <div className="text-2xl font-bold text-red-500">{failedCount + errorCount + stoppedCount}</div>
          <div className={`text-sm ${isDarkMode ? 'text-red-400' : 'text-red-700'}`}>Failed/Stopped</div>
        </div>

        <div
          className={`p-3 rounded-lg border ${isDarkMode ? 'bg-[#0d0d22]/60 border-[#2e2e60]' : 'bg-white border-sky-200'}`}>
          <div className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>
            {totalTime.toFixed(1)}s
          </div>
          <div className={`text-sm ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>Total Time</div>
        </div>
      </div>

      {/* Detailed Results */}
      <div className="space-y-2">
        <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-sky-900'}`}>Test Results:</h4>
        {results.map(result => (
          <div key={result.id} className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getStatusIcon(result.status)}
                <span className="font-medium">{result.name}</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                {result.execution_time && (
                  <span className={`${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
                    {result.execution_time.toFixed(2)}s
                  </span>
                )}
                <span
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    result.status === 'passed'
                      ? isDarkMode
                        ? 'bg-green-900/20 text-green-400'
                        : 'bg-green-100 text-green-800'
                      : result.status === 'stopped'
                        ? isDarkMode
                          ? 'bg-yellow-900/20 text-yellow-400'
                          : 'bg-yellow-100 text-yellow-800'
                        : isDarkMode
                          ? 'bg-red-900/20 text-red-400'
                          : 'bg-red-100 text-red-800'
                  }`}>
                  {result.status}
                </span>
              </div>
            </div>
            {result.error_message && (
              <div className={`mt-2 text-sm ${isDarkMode ? 'text-red-300' : 'text-red-600'}`}>
                Error: {result.error_message}
              </div>
            )}

            {/* Validator Results Section */}
            {result.validator_results && result.validator_results.length > 0 && (
              <div className="mt-2">
                <button
                  onClick={() => toggleResultExpansion(result.id)}
                  className={`flex items-center gap-1 text-xs font-medium hover:underline ${
                    isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-600 hover:text-sky-700'
                  }`}>
                  {expandedResults.has(result.id) ? (
                    <FiChevronDown className="w-3 h-3" />
                  ) : (
                    <FiChevronRight className="w-3 h-3" />
                  )}
                  Validator Results ({result.validator_results.length})
                </button>

                {expandedResults.has(result.id) && (
                  <div className={`mt-2 pl-4 border-l-2 ${isDarkMode ? 'border-[#2e2e60]' : 'border-sky-300'}`}>
                    <div className="space-y-1">
                      {result.validator_results.map((validatorResult, index) => (
                        <div
                          key={index}
                          className={`text-xs p-2 rounded ${
                            isDarkMode ? 'bg-[#0d0d22]/60 text-gray-200' : 'bg-sky-50 text-sky-700'
                          }`}>
                          <span className={`font-medium ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
                            Validation {index + 1}:
                          </span>{' '}
                          {validatorResult}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
