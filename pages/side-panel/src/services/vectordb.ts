/**
 * Vector Database Service for handling document upload and RAG functionality
 */

import { getBackendUrl } from '@extension/storage';

export interface FileUploadResponse {
  success: boolean;
  message: string;
  file_id: string;
  filename: string;
  file_url: string;
  chunks_added: number;
  user_id: string;
  project_id: string;
}

export interface QueryResult {
  score: number;
  text: string;
  filename: string;
  file_url: string;
  file_id?: string;
  chunk_index: number;
  metadata: Record<string, any>;
}

export interface QueryResponse {
  success: boolean;
  message: string;
  results: QueryResult[];
  total_results: number;
}

export interface ProjectQueryRequest {
  query: string;
  user_id?: string;
  project_id?: string;
  top_k?: number;
}

export interface FileQueryRequest {
  query: string;
  file_id: string;
  user_id?: string;
  project_id?: string;
  top_k?: number;
}

/**
 * Upload a file with user/project context
 */
export async function uploadFileWithContext(
  file: File,
  userId: string = 'demo_user',
  projectId: string = 'demo_project',
): Promise<FileUploadResponse> {
  const backendUrl = await getBackendUrl();
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user_id', userId);
  formData.append('project_id', projectId);

  const response = await fetch(`${backendUrl}/api/v1/files/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Upload failed' }));
    throw new Error(errorData.detail || 'Failed to upload file');
  }

  return response.json();
}

/**
 * Query documents within a specific project
 */
export async function queryProjectDocuments(request: ProjectQueryRequest): Promise<QueryResponse> {
  const backendUrl = await getBackendUrl();
  const response = await fetch(`${backendUrl}/api/v1/query/project`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: request.query,
      user_id: request.user_id || 'demo_user',
      project_id: request.project_id || 'demo_project',
      top_k: request.top_k || 5,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Query failed' }));
    throw new Error(errorData.detail || 'Failed to query project documents');
  }

  return response.json();
}

/**
 * Query documents within a specific file
 */
export async function queryFileDocuments(request: FileQueryRequest): Promise<QueryResponse> {
  const backendUrl = await getBackendUrl();
  const response = await fetch(`${backendUrl}/api/v1/query/file`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: request.query,
      file_id: request.file_id,
      user_id: request.user_id || 'demo_user',
      project_id: request.project_id || 'demo_project',
      top_k: request.top_k || 5,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Query failed' }));
    throw new Error(errorData.detail || 'Failed to query file documents');
  }

  return response.json();
}

/**
 * Legacy document upload (for backward compatibility)
 */
export async function uploadDocument(file: File, metadata: Record<string, any> = {}): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('metadata', JSON.stringify(metadata));

  const response = await fetch(`${BACKEND_URL}/api/v1/documents/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Upload failed' }));
    throw new Error(errorData.detail || 'Failed to upload document');
  }

  return response.json();
}

/**
 * Legacy document search (for backward compatibility)
 */
export async function searchDocuments(query: string, topK: number = 5, filter: Record<string, any> = {}): Promise<any> {
  const backendUrl = await getBackendUrl();
  const response = await fetch(`${backendUrl}/api/v1/search`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query,
      top_k: topK,
      filter_dict: filter,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Search failed' }));
    throw new Error(errorData.detail || 'Failed to search documents');
  }

  return response.json();
}
