import type { TestCase, TestCaseGenerationRequest, TestCaseExecutionRequest, TestCaseSummary } from '../types/testcase';

// Add the status update interface
export interface TestCaseStatusUpdate {
  test_id: string;
  status: 'pending' | 'running' | 'passed' | 'failed' | 'error' | 'skipped';
  execution_time?: number;
  error_message?: string;
  screenshot_path?: string;
}

import { getBackendUrl } from '@extension/storage';

export class TestCaseService {
  private baseUrl: string | null = null;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || null;
  }

  private async getBaseUrl(): Promise<string> {
    if (this.baseUrl) {
      return this.baseUrl;
    }
    return await getBackendUrl();
  }

  async generateTestCases(request: TestCaseGenerationRequest): Promise<TestCase[]> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const testCases = await response.json();
      return testCases;
    } catch (error) {
      console.error('Error generating test cases:', error);
      throw error;
    }
  }

  async getAllTestCases(): Promise<TestCase[]> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const testCases = await response.json();
      return testCases;
    } catch (error) {
      console.error('Error fetching test cases:', error);
      throw error;
    }
  }

  async getTestCaseById(testId: string): Promise<TestCase> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/${testId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const testCase = await response.json();
      return testCase;
    } catch (error) {
      console.error('Error fetching test case:', error);
      throw error;
    }
  }

  async updateTestCaseStatus(
    request: TestCaseStatusUpdate,
  ): Promise<{ message: string; test_id: string; status: string }> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error updating test case status:', error);
      throw error;
    }
  }

  async executeTestCases(request: TestCaseExecutionRequest): Promise<void> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error executing test cases:', error);
      throw error;
    }
  }

  async executeSingleTestCase(testId: string): Promise<void> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/run-single/${testId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error executing single test case:', error);
      throw error;
    }
  }

  async getTestCaseSummary(): Promise<TestCaseSummary> {
    try {
      const baseUrl = await this.getBaseUrl();
      const response = await fetch(`${baseUrl}/api/v1/testcases/summary/stats`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const summary = await response.json();
      return summary;
    } catch (error) {
      console.error('Error fetching test case summary:', error);
      throw error;
    }
  }
}

export const testCaseService = new TestCaseService();
