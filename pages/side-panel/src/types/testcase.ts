export interface TestCase {
  id: string;
  name: string;
  description: string;
  category: string;
  severity: string;
  expected_result: string;
  breaking_scenario: string;
  status: string;
  created_at: string;
  executed_at?: string;
  execution_time?: number;
  error_message?: string;
  user_prompt: string;
  selected?: boolean; // For UI state
}

export interface TestCaseGenerationRequest {
  user_prompt: string;
  generate_count?: number;
}

export interface TestCaseExecutionRequest {
  test_ids: string[];
  target_url?: string;
}

export interface TestCaseSummary {
  total_testcases: number;
  status_breakdown: {
    pending: number;
    running: number;
    passed: number;
    failed: number;
    error: number;
  };
}
