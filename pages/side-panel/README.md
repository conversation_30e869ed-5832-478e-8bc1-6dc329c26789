# Side Panel

## Overview
The **side panel** is the primary user interface for the DrCode UI Testing extension, providing a comprehensive testing dashboard that runs alongside web pages in Chrome's side panel. It serves as the central command center for creating, managing, and executing UI tests.

## Architecture
- **React Application**: Modern React app with TypeScript and hooks
- **Chrome Side Panel API**: Leverages Chrome's native side panel functionality
- **Real-time Communication**: Bidirectional messaging with content scripts
- **Persistent Interface**: Remains open while testing across different tabs

## Key Features

### Test Management Dashboard
**Purpose**: Central hub for creating, organizing, and managing tests

```tsx
// Main test dashboard interface
function TestDashboard() {
  const [tests, setTests] = useStorageState('userTests', []);
  const [activeTest, setActiveTest] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <header className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-semibold">DrCode Testing</h1>
          <div className="flex space-x-2">
            <Button size="sm" onClick={openOptions}>
              <SettingsIcon className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="primary" onClick={createNewTest}>
              <PlusIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        {activeTest ? (
          <TestRunner test={activeTest} onBack={() => setActiveTest(null)} />
        ) : (
          <TestList 
            tests={tests} 
            onSelectTest={setActiveTest}
            onDeleteTest={deleteTest}
          />
        )}
      </main>

      {/* Status Bar */}
      <footer className="border-t border-gray-200 p-3">
        <TestStatus isRunning={isRunning} />
      </footer>
    </div>
  );
}
```

### AI-Powered Test Creation
**Purpose**: Generate tests using natural language descriptions

```tsx
// AI test generation interface
function AITestGenerator() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSteps, setGeneratedSteps] = useState([]);

  const generateTest = async () => {
    setIsGenerating(true);
    try {
      const response = await aiService.generateTest({
        description: prompt,
        url: await getCurrentTabUrl(),
        context: await getPageContext(),
      });
      
      setGeneratedSteps(response.steps);
    } catch (error) {
      showNotification('Failed to generate test: ' + error.message, 'error');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <h3>AI Test Generation</h3>
        <p>Describe what you want to test in natural language</p>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-4">
          <textarea
            className="w-full h-24 p-3 border border-gray-300 rounded-md resize-none"
            placeholder="Example: Test the login form with valid credentials and verify successful login"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
          />
          
          <Button 
            onClick={generateTest}
            disabled={!prompt.trim() || isGenerating}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <LoadingIcon className="w-4 h-4 mr-2 animate-spin" />
                Generating Test...
              </>
            ) : (
              <>
                <SparklesIcon className="w-4 h-4 mr-2" />
                Generate Test
              </>
            )}
          </Button>

          {generatedSteps.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Generated Test Steps:</h4>
              <TestStepsList 
                steps={generatedSteps}
                onEditStep={editStep}
                onDeleteStep={deleteStep}
              />
              <div className="mt-4 flex space-x-2">
                <Button onClick={saveGeneratedTest}>Save Test</Button>
                <Button variant="secondary" onClick={runGeneratedTest}>
                  Run Test
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
}
```

### Interactive Test Builder
**Purpose**: Visual test step creation and editing

```tsx
// Interactive test step builder
function TestStepBuilder({ test, onUpdateTest }) {
  const [selectedElement, setSelectedElement] = useState(null);
  const [isSelectingElement, setIsSelectingElement] = useState(false);

  const startElementSelection = async () => {
    setIsSelectingElement(true);
    
    // Send message to content script to start element selection
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    chrome.tabs.sendMessage(tabs[0].id, { type: 'START_ELEMENT_SELECTION' });

    // Listen for selected element
    const handleSelection = (message) => {
      if (message.type === 'ELEMENT_SELECTED') {
        setSelectedElement(message.element);
        setIsSelectingElement(false);
        chrome.runtime.onMessage.removeListener(handleSelection);
      }
    };
    
    chrome.runtime.onMessage.addListener(handleSelection);
  };

  const addStep = (stepType: string) => {
    const newStep = {
      id: Date.now(),
      type: stepType,
      selector: selectedElement?.selector || '',
      value: '',
      timeout: 5000,
    };

    onUpdateTest({
      ...test,
      steps: [...test.steps, newStep],
    });

    setSelectedElement(null);
  };

  return (
    <div className="space-y-4">
      {/* Element Selection */}
      <Card>
        <CardHeader>
          <h4>Element Selection</h4>
        </CardHeader>
        <CardBody>
          <div className="flex items-center space-x-2">
            <Button
              onClick={startElementSelection}
              disabled={isSelectingElement}
              variant={isSelectingElement ? 'secondary' : 'primary'}
            >
              {isSelectingElement ? (
                <>
                  <CrosshairIcon className="w-4 h-4 mr-2" />
                  Selecting Element...
                </>
              ) : (
                <>
                  <CursorClickIcon className="w-4 h-4 mr-2" />
                  Select Element
                </>
              )}
            </Button>
            
            {selectedElement && (
              <div className="flex-1 text-sm bg-gray-100 p-2 rounded">
                <strong>Selected:</strong> {selectedElement.tagName.toLowerCase()}
                {selectedElement.id && `#${selectedElement.id}`}
                {selectedElement.className && `.${selectedElement.className.split(' ')[0]}`}
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Action Buttons */}
      <Card>
        <CardHeader>
          <h4>Add Test Step</h4>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={() => addStep('click')}
              disabled={!selectedElement}
              variant="outline"
            >
              <MouseIcon className="w-4 h-4 mr-2" />
              Click
            </Button>
            
            <Button
              onClick={() => addStep('type')}
              disabled={!selectedElement}
              variant="outline"
            >
              <KeyboardIcon className="w-4 h-4 mr-2" />
              Type Text
            </Button>
            
            <Button
              onClick={() => addStep('assert')}
              disabled={!selectedElement}
              variant="outline"
            >
              <CheckIcon className="w-4 h-4 mr-2" />
              Assert
            </Button>
            
            <Button
              onClick={() => addStep('wait')}
              variant="outline"
            >
              <ClockIcon className="w-4 h-4 mr-2" />
              Wait
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Test Steps List */}
      <Card>
        <CardHeader>
          <h4>Test Steps ({test.steps.length})</h4>
        </CardHeader>
        <CardBody>
          {test.steps.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No test steps yet.</p>
              <p className="text-sm">Select an element and add your first step.</p>
            </div>
          ) : (
            <div className="space-y-2">
              {test.steps.map((step, index) => (
                <TestStepCard
                  key={step.id}
                  step={step}
                  index={index}
                  onEdit={(updatedStep) => updateStep(index, updatedStep)}
                  onDelete={() => deleteStep(index)}
                />
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
```

### Real-Time Test Execution
**Purpose**: Execute tests and display live results

```tsx
// Test execution interface with real-time updates
function TestRunner({ test, onBack }) {
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(-1);
  const [results, setResults] = useState([]);
  const [screenshots, setScreenshots] = useState([]);

  const runTest = async () => {
    setIsRunning(true);
    setCurrentStep(0);
    setResults([]);
    setScreenshots([]);

    try {
      // Get active tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const tabId = tabs[0].id;

      // Execute each step
      for (let i = 0; i < test.steps.length; i++) {
        setCurrentStep(i);
        
        const step = test.steps[i];
        const startTime = Date.now();

        try {
          // Send step to content script for execution
          const result = await chrome.tabs.sendMessage(tabId, {
            type: 'EXECUTE_STEP',
            step: step,
          });

          const duration = Date.now() - startTime;
          
          setResults(prev => [...prev, {
            stepIndex: i,
            success: result.success,
            duration,
            error: result.error,
            screenshot: result.screenshot,
          }]);

          if (result.screenshot) {
            setScreenshots(prev => [...prev, {
              stepIndex: i,
              url: result.screenshot,
            }]);
          }

          // Wait between steps for stability
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          setResults(prev => [...prev, {
            stepIndex: i,
            success: false,
            duration: Date.now() - startTime,
            error: error.message,
          }]);
          
          // Stop execution on error (optional)
          if (!test.continueOnError) {
            break;
          }
        }
      }
    } finally {
      setIsRunning(false);
      setCurrentStep(-1);
    }
  };

  const stopTest = async () => {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    chrome.tabs.sendMessage(tabs[0].id, { type: 'STOP_TEST' });
    setIsRunning(false);
    setCurrentStep(-1);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeftIcon className="w-4 h-4" />
            </Button>
            <div>
              <h2 className="font-semibold">{test.name}</h2>
              <p className="text-sm text-gray-600">{test.steps.length} steps</p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            {isRunning ? (
              <Button onClick={stopTest} variant="danger" size="sm">
                <StopIcon className="w-4 h-4 mr-2" />
                Stop Test
              </Button>
            ) : (
              <Button onClick={runTest} variant="primary" size="sm">
                <PlayIcon className="w-4 h-4 mr-2" />
                Run Test
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Test Progress */}
      {isRunning && (
        <div className="border-b border-gray-200 p-4 bg-blue-50">
          <div className="flex items-center space-x-3">
            <LoadingIcon className="w-5 h-5 animate-spin text-blue-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900">
                Running test... Step {currentStep + 1} of {test.steps.length}
              </p>
              <div className="mt-1 bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentStep + 1) / test.steps.length) * 100}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Test Results */}
      <main className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {test.steps.map((step, index) => (
            <TestStepResult
              key={step.id}
              step={step}
              index={index}
              result={results.find(r => r.stepIndex === index)}
              isActive={currentStep === index}
              screenshot={screenshots.find(s => s.stepIndex === index)?.url}
            />
          ))}
        </div>
      </main>

      {/* Summary */}
      {results.length > 0 && !isRunning && (
        <footer className="border-t border-gray-200 p-4 bg-gray-50">
          <TestSummary results={results} totalSteps={test.steps.length} />
        </footer>
      )}
    </div>
  );
}
```

## Advanced Features

### Test Templates and Snippets
- Pre-built test patterns for common scenarios
- Template variables for dynamic content
- Snippet library for reusable test components

### Collaborative Testing
- Share tests with team members
- Import/export test definitions
- Version control for test modifications

### Analytics and Reporting
- Test execution history and trends
- Performance metrics and insights
- Failure analysis and debugging tools

### Integration Capabilities
- CI/CD pipeline integration
- External test management tools
- API endpoints for automated testing

## User Experience

### Responsive Design
Optimized for the narrow side panel viewport (320px width) with:
- Collapsible sections to maximize content space
- Scrollable areas for long lists
- Compact but readable information density

### Keyboard Shortcuts
- `Ctrl+Enter`: Run current test
- `Escape`: Stop running test
- `Ctrl+N`: Create new test
- `Ctrl+S`: Save current test

### Drag and Drop
- Reorder test steps by dragging
- Drop files to import test definitions
- Visual feedback during drag operations

This side panel serves as the primary interface for the DrCode UI Testing extension, providing a comprehensive testing environment that remains persistently available while users navigate and test web applications.
