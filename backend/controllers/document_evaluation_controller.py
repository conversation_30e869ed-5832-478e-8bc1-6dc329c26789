"""
Document Evaluation Controller
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
import aiofiles
import tempfile
import os

from services.document_evaluation_service import DocumentEvaluationService

logger = logging.getLogger(__name__)

# Global document evaluation service instance
evaluation_service = DocumentEvaluationService()

# Try to import python-docx for DOCX support
try:
    from docx import Document as DocxDocument
    DOCX_SUPPORT = True
except ImportError:
    DOCX_SUPPORT = False
    logger.warning("python-docx not available. DOCX files will not be supported.")

# Create router
router = APIRouter(tags=["document-evaluation"])


class DocumentEvaluationRequest(BaseModel):
    """Request model for document evaluation."""
    document_text: str = Field(..., description="The document text content to evaluate")


class FlowScore(BaseModel):
    """Model for individual flow scores."""
    flow_name: str
    score: float
    ui_element_mapping: float
    flow_coverage: float
    action_intent_mapping: float
    structure: float
    ambiguity_noise: float
    recommendations: list[str]


class DocumentEvaluationResponse(BaseModel):
    """Response model for document evaluation."""
    success: bool
    overall_score: Optional[float] = None
    flow_scores: Optional[list[FlowScore]] = None
    global_recommendations: Optional[list[str]] = None
    error: Optional[str] = None


@router.post("/evaluate-document", response_model=DocumentEvaluationResponse)
async def evaluate_document_text(request: DocumentEvaluationRequest):
    """
    Evaluate document text based on UI testing criteria
    """
    try:
        logger.info("Starting document evaluation for text input")
        
        # Validate input
        if not request.document_text.strip():
            raise HTTPException(status_code=400, detail="Document text cannot be empty")
        
        # Perform evaluation
        evaluation_result = evaluation_service.evaluate_document(request.document_text)
        
        # Check if there was an error in the evaluation
        if "error" in evaluation_result:
            return DocumentEvaluationResponse(
                success=False,
                error=evaluation_result["error"]
            )
        
        # Extract flow scores
        flow_scores = []
        if "flow_scores" in evaluation_result:
            for flow in evaluation_result["flow_scores"]:
                flow_scores.append(FlowScore(**flow))
        
        logger.info(f"Document evaluation completed with overall score: {evaluation_result.get('overall_score', 'N/A')}")
        
        return DocumentEvaluationResponse(
            success=True,
            overall_score=evaluation_result.get("overall_score"),
            flow_scores=flow_scores,
            global_recommendations=evaluation_result.get("global_recommendations", [])
        )
        
    except ValueError as e:
        logger.error(f"Configuration error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Error evaluating document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Document evaluation failed: {str(e)}")


@router.post("/evaluate-document-file")
async def evaluate_document_file(file: UploadFile = File(...)):
    """
    Evaluate uploaded document file based on UI testing criteria
    """
    try:
        logger.info(f"Starting document evaluation for uploaded file: {file.filename}")
        
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file uploaded")
        
        # Check file type
        allowed_extensions = {'.txt', '.md', '.docx'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type. Allowed types: {', '.join(allowed_extensions)}"
            )
        
        # Read file content
        content = await file.read()
        
        # Handle different file types
        if file_ext == '.txt' or file_ext == '.md':
            document_text = content.decode('utf-8')
        elif file_ext == '.docx':
            if not DOCX_SUPPORT:
                raise HTTPException(
                    status_code=400, 
                    detail="DOCX file processing not supported. Please install python-docx or convert to TXT/MD format."
                )
            
            # Save content to a temporary file and process with python-docx
            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                temp_file.write(content)
                temp_file.flush()
                
                try:
                    doc = DocxDocument(temp_file.name)
                    document_text = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
                except Exception as e:
                    logger.error(f"Error processing DOCX file: {str(e)}")
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Failed to process DOCX file: {str(e)}"
                    )
                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(temp_file.name)
                    except OSError:
                        pass
        else:
            raise HTTPException(status_code=400, detail="Unsupported file format")
        
        if not document_text.strip():
            raise HTTPException(status_code=400, detail="Document appears to be empty")
        
        # Perform evaluation
        evaluation_result = evaluation_service.evaluate_document(document_text)
        
        # Check if there was an error in the evaluation
        if "error" in evaluation_result:
            # Determine appropriate status code based on error type
            error_message = evaluation_result["error"]
            if "OpenAI API error" in error_message:
                status_code = 503  # Service Unavailable for external API issues
            elif "Failed to parse" in error_message:
                status_code = 502  # Bad Gateway for parsing issues
            else:
                status_code = 500  # Internal Server Error for other issues
                
            return JSONResponse(
                status_code=status_code,
                content={
                    "success": False,
                    "error": evaluation_result["error"],
                    "details": evaluation_result.get("details", "Please try again or contact support if the issue persists")
                }
            )
        
        # Extract flow scores
        flow_scores = []
        if "flow_scores" in evaluation_result:
            for flow in evaluation_result["flow_scores"]:
                flow_scores.append({
                    "flow_name": flow.get("flow_name", "Unknown Flow"),
                    "score": flow.get("score", 0),
                    "ui_element_mapping": flow.get("ui_element_mapping", 0),
                    "flow_coverage": flow.get("flow_coverage", 0),
                    "action_intent_mapping": flow.get("action_intent_mapping", 0),
                    "structure": flow.get("structure", 0),
                    "ambiguity_noise": flow.get("ambiguity_noise", 0),
                    "recommendations": flow.get("recommendations", [])
                })
        
        logger.info(f"Document evaluation completed for {file.filename} with overall score: {evaluation_result.get('overall_score', 'N/A')}")
        
        return JSONResponse(content={
            "success": True,
            "filename": file.filename,
            "overall_score": evaluation_result.get("overall_score"),
            "flow_scores": flow_scores,
            "global_recommendations": evaluation_result.get("global_recommendations", [])
        })
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Configuration error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Error evaluating document file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Document evaluation failed: {str(e)}")


@router.get("/evaluation-criteria")
async def get_evaluation_criteria():
    """
    Get the evaluation criteria used for document assessment
    """
    return {
        "criteria": [
            {
                "name": "UI Element Mapping",
                "weight": 30,
                "description": "How clearly and consistently UI elements are described (e.g., buttons, input fields, labels, dropdowns). Look for clarity, naming consistency, and specificity of elements mentioned."
            },
            {
                "name": "Flow Coverage",
                "weight": 30,
                "description": "How many user flows are described fully (steps, conditions, expected results). Consider if happy paths, edge cases, and failure paths are mentioned."
            },
            {
                "name": "Action-Intent Mapping",
                "weight": 20,
                "description": "Whether actions are linked to their business purpose or intent. E.g., 'Click Submit to finalize the order and trigger backend validation.'"
            },
            {
                "name": "Consistency + Structure",
                "weight": 15,
                "description": "Whether the document is well-structured and chunkable (e.g., flow headers, numbered steps, clear sectioning)."
            },
            {
                "name": "Ambiguity / Noise",
                "weight": 5,
                "description": "Evaluate whether the document avoids vague language, contradictions, or irrelevant content. High score means the content is clear, direct, and unambiguous."
            }
        ],
        "total_weight": 100
    }
