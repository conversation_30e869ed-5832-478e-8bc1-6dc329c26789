"""
UI Testcase Controller

FastAPI controller for managing UI test case generation and execution
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging

from services.testcase_service import TestcaseService, TestStatus
from services.browser_automation import BrowserAutomationService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/testcases", tags=["testcases"])

# Initialize services
testcase_service = TestcaseService()
browser_automation_service = BrowserAutomationService()


class TestcaseGenerationRequest(BaseModel):
    """Request model for generating test cases"""
    user_prompt: str
    generate_count: Optional[int] = 5


class TestcaseExecutionRequest(BaseModel):
    """Request model for executing test cases"""
    test_ids: List[str]
    target_url: Optional[str] = None


class TestcaseStatusUpdate(BaseModel):
    """Request model for updating test case status"""
    test_id: str
    status: TestStatus
    execution_time: Optional[float] = None
    error_message: Optional[str] = None
    screenshot_path: Optional[str] = None


@router.post("/generate", response_model=List[Dict[str, Any]])
async def generate_testcases(request: TestcaseGenerationRequest):
    """
    Generate UI test cases based on user prompt
    
    This endpoint creates comprehensive test cases that can potentially break the UI,
    covering various scenarios like input validation, responsiveness, performance, etc.
    """
    try:
        logger.info(f"Generating test cases for prompt: {request.user_prompt}")
        
        if not request.user_prompt.strip():
            raise HTTPException(status_code=400, detail="User prompt cannot be empty")
        
        # Generate test cases using the service
        test_cases = await testcase_service.generate_testcases(request.user_prompt)
        
        if not test_cases:
            raise HTTPException(status_code=500, detail="Failed to generate test cases")
        
        # Convert to response format
        response = []
        for test_case in test_cases:
            response.append({
                "id": test_case.id,
                "name": test_case.name,
                "description": test_case.description,
                "category": test_case.category,
                "severity": test_case.severity.value,
                "expected_result": test_case.expected_result,
                "breaking_scenario": test_case.breaking_scenario,
                "status": test_case.status.value,
                "created_at": test_case.created_at.isoformat(),
                "user_prompt": test_case.user_prompt
            })
        
        logger.info(f"Successfully generated {len(test_cases)} test cases")
        return response
        
    except Exception as e:
        logger.error(f"Error generating test cases: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate test cases: {str(e)}")


@router.get("/", response_model=List[Dict[str, Any]])
async def get_all_testcases():
    """
    Get all stored test cases with their current status
    """
    try:
        test_cases = await testcase_service.get_all_testcases()
        
        response = []
        for test_case in test_cases:
            response.append({
                "id": test_case.id,
                "name": test_case.name,
                "description": test_case.description,
                "category": test_case.category,
                "severity": test_case.severity.value,
                "expected_result": test_case.expected_result,
                "breaking_scenario": test_case.breaking_scenario,
                "status": test_case.status.value,
                "created_at": test_case.created_at.isoformat(),
                "executed_at": test_case.executed_at.isoformat() if test_case.executed_at else None,
                "execution_time": test_case.execution_time,
                "error_message": test_case.error_message,
                "user_prompt": test_case.user_prompt
            })
        
        return response
        
    except Exception as e:
        logger.error(f"Error retrieving test cases: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve test cases: {str(e)}")


@router.get("/{test_id}", response_model=Dict[str, Any])
async def get_testcase_by_id(test_id: str):
    """
    Get a specific test case by ID
    """
    try:
        test_case = await testcase_service.get_testcase_by_id(test_id)
        
        if not test_case:
            raise HTTPException(status_code=404, detail=f"Test case {test_id} not found")
        
        return {
            "id": test_case.id,
            "name": test_case.name,
            "description": test_case.description,
            "category": test_case.category,
            "severity": test_case.severity.value,
            "expected_result": test_case.expected_result,
            "breaking_scenario": test_case.breaking_scenario,
            "status": test_case.status.value,
            "created_at": test_case.created_at.isoformat(),
            "executed_at": test_case.executed_at.isoformat() if test_case.executed_at else None,
            "execution_time": test_case.execution_time,
            "error_message": test_case.error_message,
            "screenshot_path": test_case.screenshot_path,
            "user_prompt": test_case.user_prompt
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving test case {test_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve test case: {str(e)}")


@router.post("/execute")
async def execute_testcases(request: TestcaseExecutionRequest, background_tasks: BackgroundTasks):
    """
    Execute test cases through browser automation
    
    This endpoint triggers the execution of specified test cases through the Chrome extension,
    running them one by one and updating their status.
    """
    try:
        logger.info(f"Executing test cases: {request.test_ids}")
        
        if not request.test_ids:
            raise HTTPException(status_code=400, detail="No test IDs provided")
        
        # Validate that all test cases exist
        for test_id in request.test_ids:
            test_case = await testcase_service.get_testcase_by_id(test_id)
            if not test_case:
                raise HTTPException(status_code=404, detail=f"Test case {test_id} not found")
        
        # Start background execution
        background_tasks.add_task(
            _execute_testcases_background,
            request.test_ids,
            request.target_url
        )
        
        return {
            "message": f"Started execution of {len(request.test_ids)} test cases",
            "test_ids": request.test_ids,
            "status": "execution_started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting test case execution: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start test execution: {str(e)}")


@router.put("/status")
async def update_testcase_status(request: TestcaseStatusUpdate):
    """
    Update the status of a test case
    """
    try:
        success = await testcase_service.update_testcase_status(
            test_id=request.test_id,
            status=request.status,
            execution_time=request.execution_time,
            error_message=request.error_message,
            screenshot_path=request.screenshot_path
        )
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Test case {request.test_id} not found")
        
        return {
            "message": f"Test case {request.test_id} status updated to {request.status.value}",
            "test_id": request.test_id,
            "status": request.status.value
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating test case status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update test case status: {str(e)}")


@router.get("/summary/stats")
async def get_testcases_summary():
    """
    Get summary statistics of all test cases
    """
    try:
        summary = await testcase_service.get_testcases_summary()
        return summary
        
    except Exception as e:
        logger.error(f"Error retrieving test cases summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve summary: {str(e)}")


@router.delete("/{test_id}")
async def delete_testcase(test_id: str):
    """
    Delete a specific test case (placeholder for future implementation)
    """
    # This would require implementation in the service layer
    raise HTTPException(status_code=501, detail="Test case deletion not yet implemented")


@router.post("/run-single/{test_id}")
async def run_single_testcase(test_id: str, background_tasks: BackgroundTasks, target_url: Optional[str] = None):
    """
    Execute a single test case
    """
    try:
        test_case = await testcase_service.get_testcase_by_id(test_id)
        if not test_case:
            raise HTTPException(status_code=404, detail=f"Test case {test_id} not found")
        
        # Start background execution for single test
        background_tasks.add_task(
            _execute_testcases_background,
            [test_id],
            target_url
        )
        
        return {
            "message": f"Started execution of test case {test_id}",
            "test_id": test_id,
            "test_name": test_case.name,
            "status": "execution_started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting single test execution: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start test execution: {str(e)}")


async def _execute_testcases_background(test_ids: List[str], target_url: Optional[str] = None):
    """
    Background task to execute test cases one by one
    """
    try:
        logger.info(f"Starting background execution of {len(test_ids)} test cases")
        
        for test_id in test_ids:
            try:
                # Update status to running
                await testcase_service.update_testcase_status(test_id, TestStatus.RUNNING)
                
                # Get test case details
                test_case = await testcase_service.get_testcase_by_id(test_id)
                if not test_case:
                    logger.error(f"Test case {test_id} not found during execution")
                    continue
                
                # Execute the test case through browser automation
                execution_result = await browser_automation_service.execute_testcase(
                    test_case, target_url
                )
                
                # Update status based on execution result
                if execution_result["success"]:
                    await testcase_service.update_testcase_status(
                        test_id=test_id,
                        status=TestStatus.PASSED if execution_result["passed"] else TestStatus.FAILED,
                        execution_time=execution_result.get("execution_time"),
                        screenshot_path=execution_result.get("screenshot_path")
                    )
                else:
                    await testcase_service.update_testcase_status(
                        test_id=test_id,
                        status=TestStatus.ERROR,
                        error_message=execution_result.get("error_message"),
                        execution_time=execution_result.get("execution_time")
                    )
                
                logger.info(f"Completed execution of test case {test_id}")
                
            except Exception as e:
                logger.error(f"Error executing test case {test_id}: {e}")
                await testcase_service.update_testcase_status(
                    test_id=test_id,
                    status=TestStatus.ERROR,
                    error_message=str(e)
                )
        
        logger.info(f"Completed background execution of all test cases")
        
    except Exception as e:
        logger.error(f"Error in background test execution: {e}")


# Health check endpoint
@router.get("/health")
async def health_check():
    """
    Health check for testcase service
    """
    return {
        "status": "healthy",
        "service": "testcase_service",
        "timestamp": logger.name
    }
