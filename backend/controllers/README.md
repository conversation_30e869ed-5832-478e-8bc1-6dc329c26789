# Controllers

## Overview
This directory contains **FastAPI route controllers** that handle HTTP requests and responses for the DrCode UI Testing API server. Controllers act as the entry point for API endpoints and orchestrate calls to service layers.

## Architecture Pattern
- **Controller Layer**: Handles HTTP concerns (request/response, validation, status codes)
- **Service Layer**: Contains business logic (called by controllers)
- **Dependency Injection**: Uses FastAPI's dependency system
- **Error Handling**: Standardized HTTP exceptions and responses

## Controllers

### `auto_refinement_controller.py`
**Purpose**: Manages automatic prompt refinement endpoints
**Endpoints**:
- Auto-refinement request processing
- Prompt improvement suggestions
- Vector-based refinement algorithms

**Key Features**:
- Pydantic models for request validation
- Async request handling
- Integration with AutoRefinementService

### `document_evaluation_controller.py`
**Purpose**: Handles document analysis and evaluation endpoints
**Endpoints**:
- Document quality assessment
- Content scoring and metrics
- Evaluation result retrieval

**Key Features**:
- Multi-format document support
- Async evaluation processing
- Structured evaluation responses

### `vector_db_controller.py`
**Purpose**: Manages vector database operations
**Endpoints**:
- Vector storage and retrieval
- Similarity search operations
- Database health and status

**Key Features**:
- Pinecone integration
- Vector embedding management
- Search result ranking

### `health_controller.py`
**Purpose**: Provides system health monitoring
**Endpoints**:
- Service health checks
- Database connectivity status
- Performance metrics

**Key Features**:
- Non-blocking health checks
- Detailed status reporting
- Dependency verification

## Development Patterns

### Request/Response Models
```python
class RequestModel(BaseModel):
    field: str = Field(..., description="Field description")

class ResponseModel(BaseModel):
    result: str
    status: str
```

### Error Handling
```python
try:
    result = await service.process()
    return ResponseModel(result=result, status="success")
except Exception as e:
    raise HTTPException(status_code=500, detail=str(e))
```

### Async Operations
All controllers use async/await patterns for non-blocking I/O operations with external services and databases.
