"""
Vector Database Controller for managing Pinecone database entries
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from services.vector_db_service import VectorDBService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["vector-db"])

# Global vector DB service instance
vector_db_service = VectorDBService()


class DocumentRequest(BaseModel):
    """Request model for adding a document."""
    text: str = Field(..., description="The document text content")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Optional metadata")


class DocumentResponse(BaseModel):
    """Response model for document operations."""
    success: bool
    message: str
    document_id: Optional[str] = None


class SearchRequest(BaseModel):
    """Request model for searching documents."""
    query: str = Field(..., description="Search query")
    top_k: int = Field(default=5, description="Number of results to return")
    filter_dict: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata filter")


class SearchResult(BaseModel):
    """Search result model."""
    id: str
    text: str
    score: float
    metadata: Dict[str, Any]


class SearchResponse(BaseModel):
    """Response model for search operations."""
    success: bool
    message: str
    results: List[SearchResult]


class DatabaseStatsResponse(BaseModel):
    """Response model for database statistics."""
    success: bool
    message: str
    total_vectors: int
    dimension: int
    index_fullness: float


class FileUploadRequest(BaseModel):
    """Request model for file upload with context."""
    user_id: Optional[str] = Field(default="demo_user", description="User identifier")
    project_id: Optional[str] = Field(default="demo_project", description="Project identifier")


class FileUploadResponse(BaseModel):
    """Response model for file upload."""
    success: bool
    message: str
    file_id: str
    filename: str
    file_url: str
    chunks_added: int
    user_id: str
    project_id: str


class ProjectQueryRequest(BaseModel):
    """Request model for project-based queries."""
    query: str = Field(..., description="Search query text")
    user_id: Optional[str] = Field(default="demo_user", description="User identifier")
    project_id: Optional[str] = Field(default="demo_project", description="Project identifier")
    top_k: Optional[int] = Field(default=5, description="Number of results to return")


class FileQueryRequest(BaseModel):
    """Request model for file-based queries."""
    query: str = Field(..., description="Search query text")
    file_id: str = Field(..., description="File identifier")
    user_id: Optional[str] = Field(default="demo_user", description="User identifier")
    project_id: Optional[str] = Field(default="demo_project", description="Project identifier")
    top_k: Optional[int] = Field(default=5, description="Number of results to return")


class QueryResponse(BaseModel):
    """Response model for query results."""
    success: bool
    message: str
    results: List[Dict[str, Any]]
    total_results: int


class FileListRequest(BaseModel):
    """Request model for listing files."""
    user_id: Optional[str] = Field(default="demo_user", description="User identifier")
    project_id: Optional[str] = Field(default="demo_project", description="Project identifier")


class FileListResponse(BaseModel):
    """Response model for file listing."""
    success: bool
    message: str
    files: List[Dict[str, Any]]
    total_files: int


class FileDeleteRequest(BaseModel):
    """Request model for deleting a file."""
    file_id: str = Field(..., description="File identifier to delete")
    user_id: Optional[str] = Field(default="demo_user", description="User identifier")
    project_id: Optional[str] = Field(default="demo_project", description="Project identifier")


class FileDeleteResponse(BaseModel):
    """Response model for file deletion."""
    success: bool
    message: str
    file_id: str


@router.post("/documents", response_model=DocumentResponse)
async def add_document(request: DocumentRequest):
    """Add a single document to the vector database."""
    try:
        logger.info(f"Adding document: {request.text[:100]}...")
        
        result = await vector_db_service.add_document(
            text=request.text,
            metadata=request.metadata
        )
        
        return DocumentResponse(
            success=True,
            message="Document added successfully",
            document_id=result["document_id"]
        )
        
    except Exception as e:
        logger.error(f"Failed to add document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add document: {str(e)}")


@router.post("/documents/batch", response_model=DocumentResponse)
async def add_documents_batch(documents: List[DocumentRequest]):
    """Add multiple documents to the vector database."""
    try:
        logger.info(f"Adding {len(documents)} documents...")
        
        await vector_db_service.add_documents_batch(
            documents=[{"text": doc.text, "metadata": doc.metadata} for doc in documents]
        )

        return DocumentResponse(
            success=True,
            message=f"Successfully added {len(documents)} documents",
            document_id=None
        )
        
    except Exception as e:
        logger.error(f"Failed to add documents batch: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add documents batch: {str(e)}")


@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...)
):
    """Upload and process a document file (legacy endpoint using new implementation)."""
    try:
        # Use new implementation with default user/project values for backward compatibility
        result = await vector_db_service.upload_file_with_context(
            file=file,
            user_id="demo_user",
            project_id="demo_project"
        )

        return DocumentResponse(
            success=True,
            message=f"File processed and {result['chunks_added']} chunks added",
            document_id=result.get('file_id')
        )

    except Exception as e:
        logger.error(f"Failed to upload document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload document: {str(e)}")


@router.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """Search for similar documents in the vector database (legacy endpoint using new implementation)."""
    try:
        logger.info(f"Searching for: {request.query[:100]}...")

        # Use new implementation with default user/project values for backward compatibility
        results = await vector_db_service.query_project_documents(
            query=request.query,
            user_id="demo_user",
            project_id="demo_project",
            top_k=request.top_k
        )

        # Convert new format to legacy format
        search_results = [
            SearchResult(
                id=result.get("file_id", "unknown"),
                text=result["text"],
                score=result["score"],
                metadata=result.get("metadata", {})
            )
            for result in results
        ]

        return SearchResponse(
            success=True,
            message=f"Found {len(search_results)} results",
            results=search_results
        )

    except Exception as e:
        logger.error(f"Search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document from the vector database."""
    try:
        logger.info(f"Deleting document: {document_id}")
        
        await vector_db_service.delete_document(document_id)
        
        return DocumentResponse(
            success=True,
            message="Document deleted successfully",
            document_id=document_id
        )
        
    except Exception as e:
        logger.error(f"Failed to delete document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")


@router.get("/stats", response_model=DatabaseStatsResponse)
async def get_database_stats():
    """Get database statistics."""
    try:
        stats = await vector_db_service.get_database_stats()
        
        return DatabaseStatsResponse(
            success=True,
            message="Database stats retrieved successfully",
            total_vectors=stats["total_vectors"],
            dimension=stats["dimension"],
            index_fullness=stats["index_fullness"]
        )
        
    except Exception as e:
        logger.error(f"Failed to get database stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get database stats: {str(e)}")


@router.delete("/clear")
async def clear_database():
    """Clear all documents from the vector database."""
    try:
        logger.warning("Clearing entire vector database...")
        
        await vector_db_service.clear_database()
        
        return DocumentResponse(
            success=True,
            message="Database cleared successfully",
            document_id=None
        )
        
    except Exception as e:
        logger.error(f"Failed to clear database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear database: {str(e)}")


@router.post("/files/upload", response_model=FileUploadResponse)
async def upload_file_with_context(
    file: UploadFile = File(...),
    user_id: Optional[str] = Form(default="demo_user"),
    project_id: Optional[str] = Form(default="demo_project")
):
    """Upload and process a file with user/project context."""
    try:
        logger.info(f"Uploading file {file.filename} for user {user_id}, project {project_id}")

        result = await vector_db_service.upload_file_with_context(
            file=file,
            user_id=user_id,
            project_id=project_id
        )

        return FileUploadResponse(
            success=True,
            message=result["message"],
            file_id=result["file_id"],
            filename=result["filename"],
            file_url=result["file_url"],
            chunks_added=result["chunks_added"],
            user_id=result["user_id"],
            project_id=result["project_id"]
        )

    except Exception as e:
        logger.error(f"Failed to upload file with context: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")


@router.post("/query/project", response_model=QueryResponse)
async def query_project_documents(request: ProjectQueryRequest):
    """Query documents within a specific project."""
    try:
        logger.info(f"Querying project {request.project_id} for: {request.query[:100]}...")

        results = await vector_db_service.query_project_documents(
            query=request.query,
            user_id=request.user_id,
            project_id=request.project_id,
            top_k=request.top_k
        )

        return QueryResponse(
            success=True,
            message=f"Found {len(results)} results in project {request.project_id}",
            results=results,
            total_results=len(results)
        )

    except Exception as e:
        logger.error(f"Failed to query project documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to query project: {str(e)}")


@router.post("/query/file", response_model=QueryResponse)
async def query_file_documents(request: FileQueryRequest):
    """Query documents within a specific file."""
    try:
        logger.info(f"Querying file {request.file_id} for: {request.query[:100]}...")

        results = await vector_db_service.query_file_documents(
            query=request.query,
            file_id=request.file_id,
            user_id=request.user_id,
            project_id=request.project_id,
            top_k=request.top_k
        )

        return QueryResponse(
            success=True,
            message=f"Found {len(results)} results in file {request.file_id}",
            results=results,
            total_results=len(results)
        )

    except Exception as e:
        logger.error(f"Failed to query file documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to query file: {str(e)}")


@router.post("/files/list", response_model=FileListResponse)
async def list_files_by_project(request: FileListRequest):
    """List all files in a project."""
    try:
        logger.info(f"Listing files for user {request.user_id}, project {request.project_id}")

        files = await vector_db_service.list_files_by_project(
            user_id=request.user_id,
            project_id=request.project_id
        )

        return FileListResponse(
            success=True,
            message=f"Found {len(files)} files in project {request.project_id}",
            files=files,
            total_files=len(files)
        )

    except Exception as e:
        logger.error(f"Failed to list files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")


@router.delete("/files/{file_id}", response_model=FileDeleteResponse)
async def delete_file_by_id(
    file_id: str,
    user_id: Optional[str] = "demo_user",
    project_id: Optional[str] = "demo_project"
):
    """Delete a file and all its chunks."""
    try:
        logger.info(f"Deleting file {file_id} for user {user_id}, project {project_id}")

        result = await vector_db_service.delete_file_by_id(
            file_id=file_id,
            user_id=user_id,
            project_id=project_id
        )

        return FileDeleteResponse(
            success=result["success"],
            message=result["message"],
            file_id=file_id
        )

    except Exception as e:
        logger.error(f"Failed to delete file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete file: {str(e)}")


@router.get("/files/{file_id}/metadata")
async def get_file_metadata(
    file_id: str,
    user_id: Optional[str] = "demo_user",
    project_id: Optional[str] = "demo_project"
):
    """Get metadata for a specific file."""
    try:
        logger.info(f"Getting metadata for file {file_id}")

        metadata = await vector_db_service.get_file_metadata(
            file_id=file_id,
            user_id=user_id,
            project_id=project_id
        )

        if metadata:
            return {
                "success": True,
                "message": "File metadata retrieved successfully",
                "metadata": metadata
            }
        else:
            raise HTTPException(status_code=404, detail=f"File {file_id} not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get file metadata: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get file metadata: {str(e)}")
