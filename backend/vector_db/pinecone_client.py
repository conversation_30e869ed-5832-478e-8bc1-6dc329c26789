"""
Pinecone Vector Database Client with Gemini Embeddings
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from pinecone import Pinecone, ServerlessSpec
import time
import hashlib

logger = logging.getLogger(__name__)


class PineconeVectorDB:
    """Pinecone vector database client with Gemini embeddings."""
    
    def __init__(self, index_name: str = os.getenv("PINECONE_INDEX_NAME", "drcode")):
        """Initialize the Pinecone client."""
        self.index_name = index_name
        self.index = None
        self.pc = None
        
        # Initialize Pinecone
        api_key = os.getenv("PINECONE_API_KEY")
        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable is required")
        
        self.pc = Pinecone(api_key=api_key)
        
        # Initialize Gemini
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        genai.configure(api_key=gemini_api_key)
        
        logger.info("Pinecone and Gemini clients initialized")
    
    def create_index_if_not_exists(self):
        """Create index if it doesn't exist."""
        try:
            # Check if index exists
            existing_indexes = [index.name for index in self.pc.list_indexes()]
            
            if self.index_name not in existing_indexes:
                logger.info(f"Creating index: {self.index_name}")
                
                # Create index with serverless spec
                self.pc.create_index(
                    name=self.index_name,
                    dimension=768,  # Gemini embedding dimension
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-east-1"
                    )
                )
                
                # Wait for index to be ready
                while not self.pc.describe_index(self.index_name).status['ready']:
                    logger.info("Waiting for index to be ready...")
                    time.sleep(1)
                
                logger.info(f"Index {self.index_name} created successfully")
            else:
                logger.info(f"Index {self.index_name} already exists")
            
            # Connect to index
            self.index = self.pc.Index(self.index_name)
            logger.info(f"Connected to index: {self.index_name}")
            
        except Exception as e:
            logger.error(f"Error creating/connecting to index: {e}")
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using Gemini."""
        try:
            result = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error getting embedding: {e}")
            raise
    
    def generate_id(self, text: str) -> str:
        """Generate a unique ID for text content."""
        return hashlib.md5(text.encode()).hexdigest()
    
    def upsert_documents(self, documents: List[Dict[str, Any]]):
        """
        Upsert documents to the vector database (legacy method using new implementation).

        Args:
            documents: List of documents with 'text' and 'metadata' keys
        """
        if not self.index:
            self.create_index_if_not_exists()

        # Use new implementation with default user/project values for backward compatibility
        for i, doc in enumerate(documents):
            text = doc['text']
            metadata = doc.get('metadata', {})

            logger.info(f"Processing document {i+1}/{len(documents)}: {text[:100]}...")

            # Use new index_file_chunk method with default values
            self.index_file_chunk(
                user_id="demo_user",
                project_id="demo_project",
                file_id=f"legacy_doc_{i}",
                filename=metadata.get('filename', f'document_{i}.txt'),
                file_url=metadata.get('file_url', f'legacy://document_{i}'),
                chunk_text=text,
                chunk_index=i,
                metadata_type="API"  # Mark as API type for legacy documents
            )

        logger.info(f"Successfully upserted {len(documents)} documents using new implementation")
    
    def search(self, query: str, top_k: int = 5, filter_dict: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Search for similar documents (legacy method using new implementation).

        Args:
            query: Search query
            top_k: Number of results to return
            filter_dict: Optional metadata filter

        Returns:
            List of matching documents with scores
        """
        if not self.index:
            self.create_index_if_not_exists()

        # Use new implementation with default user/project values for backward compatibility
        results = self.query_by_project(
            user_id="demo_user",
            project_id="demo_project",
            query_text=query,
            top_k=top_k
        )

        # Convert new format to legacy format
        formatted_results = []
        for result in results:
            formatted_results.append({
                'id': result.get('file_id', 'unknown'),
                'score': result['score'],
                'text': result['text'],
                'metadata': result.get('metadata', {})
            })

        logger.info(f"Search returned {len(formatted_results)} results using new implementation")
        return formatted_results
    
    def delete_document(self, document_id: str):
        """Delete a document by ID."""
        if not self.index:
            self.create_index_if_not_exists()
        
        self.index.delete(ids=[document_id])
        logger.info(f"Deleted document: {document_id}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get index statistics."""
        if not self.index:
            self.create_index_if_not_exists()
        
        stats = self.index.describe_index_stats()
        return {
            'total_vectors': stats['total_vector_count'],
            'dimension': stats['dimension'],
            'index_fullness': stats['index_fullness']
        }
    
    def clear_all(self):
        """Clear all vectors from the index."""
        if not self.index:
            self.create_index_if_not_exists()

        # Delete all vectors
        self.index.delete(delete_all=True)
        logger.info("Cleared all vectors from index")
    
    def clear_namespace(self, user_id: str):
        """Clear all vectors from a specific namespace."""
        if not self.index:
            self.create_index_if_not_exists()

        try:
            # Check if namespace exists by trying to get stats first
            stats = self.index.describe_index_stats()
            namespaces = stats.get('namespaces', {})
            
            if user_id not in namespaces:
                logger.info(f"Namespace {user_id} does not exist or is already empty")
                return
            
            # Delete all vectors in the namespace
            self.index.delete(namespace=user_id, delete_all=True)
            logger.info(f"Cleared all vectors from namespace: {user_id}")
        except Exception as e:
            # If it's a "namespace not found" error, that's actually okay - it means it's already empty
            if "Namespace not found" in str(e) or "namespace not found" in str(e).lower():
                logger.info(f"Namespace {user_id} was already empty or didn't exist")
                return
            logger.error(f"Error clearing namespace {user_id}: {e}")
            raise

    def index_file_chunk(self, user_id: str, project_id: str, file_id: str, filename: str,
                        file_url: str, text_chunk: str, chunk_index: int) -> str:
        """
        Index a file chunk with the new schema structure.

        Args:
            user_id: User identifier for namespace
            project_id: Project identifier
            file_id: File identifier
            filename: Original filename
            file_url: URL/path to the original file
            text_chunk: Text content of the chunk
            chunk_index: Index of this chunk within the file

        Returns:
            The ID of the indexed chunk
        """
        if not self.index:
            self.create_index_if_not_exists()

        # Generate embedding for the text chunk
        embedding = self.get_embedding(text_chunk)

        # Create unique chunk ID
        chunk_id = f"{file_id}_chunk_{chunk_index}"

        # Prepare metadata
        metadata = {
            "user_id": user_id,
            "project_id": project_id,
            "file_id": file_id,
            "filename": filename,
            "file_url": file_url,
            "chunk_index": chunk_index,
            "chunk_text": text_chunk,
            "created_at": datetime.now().isoformat(),
            "type": "UI"
        }

        # Upsert to Pinecone with namespace
        self.index.upsert(
            namespace=user_id,
            vectors=[{
                "id": chunk_id,
                "values": embedding,
                "metadata": metadata
            }]
        )

        logger.info(f"Indexed chunk {chunk_id} for user {user_id}, project {project_id}")
        return chunk_id

    def query_by_project(self, user_id: str, project_id: str, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Query documents by project.

        Args:
            user_id: User identifier for namespace
            project_id: Project identifier to filter by
            query_text: Text to search for
            top_k: Number of results to return

        Returns:
            List of matching chunks with metadata
        """
        if not self.index:
            self.create_index_if_not_exists()

        # Generate embedding for query
        embedding = self.get_embedding(query_text)

        # Query with project filter
        results = self.index.query(
            namespace=user_id,
            vector=embedding,
            top_k=top_k,
            include_metadata=True,
            filter={"project_id": project_id, "type": "UI"}
        )

        # Format results
        formatted_results = []
        for match in results.matches:
            formatted_results.append({
                "score": match.score,
                "text": match.metadata.get("chunk_text", ""),
                "filename": match.metadata.get("filename", ""),
                "file_url": match.metadata.get("file_url", ""),
                "file_id": match.metadata.get("file_id", ""),
                "chunk_index": match.metadata.get("chunk_index", 0),
                "metadata": match.metadata
            })

        logger.info(f"Project query returned {len(formatted_results)} results for project {project_id}")
        return formatted_results

    def query_by_file(self, user_id: str, project_id: str, file_id: str, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Query documents by specific file.

        Args:
            user_id: User identifier for namespace
            project_id: Project identifier
            file_id: File identifier to filter by
            query_text: Text to search for
            top_k: Number of results to return

        Returns:
            List of matching chunks from the specific file
        """
        if not self.index:
            self.create_index_if_not_exists()

        # Generate embedding for query
        embedding = self.get_embedding(query_text)

        # Query with project and file filter
        results = self.index.query(
            namespace=user_id,
            vector=embedding,
            top_k=top_k,
            include_metadata=True,
            filter={"project_id": project_id, "file_id": file_id, "type": "UI"}
        )

        # Format results
        formatted_results = []
        for match in results.matches:
            formatted_results.append({
                "score": match.score,
                "text": match.metadata.get("chunk_text", ""),
                "filename": match.metadata.get("filename", ""),
                "file_url": match.metadata.get("file_url", ""),
                "chunk_index": match.metadata.get("chunk_index", 0),
                "metadata": match.metadata
            })

        logger.info(f"File query returned {len(formatted_results)} results for file {file_id}")
        return formatted_results

    def list_files_by_project(self, user_id: str, project_id: str) -> List[Dict[str, Any]]:
        """
        List all files in a project.

        Args:
            user_id: User identifier for namespace
            project_id: Project identifier to filter by

        Returns:
            List of unique files with metadata
        """
        if not self.index:
            self.create_index_if_not_exists()

        # Query all vectors in the namespace with project filter
        # We'll use a dummy vector to get all results, then filter unique files
        dummy_embedding = [0.0] * 768  # Zero vector for listing purposes

        results = self.index.query(
            namespace=user_id,
            vector=dummy_embedding,
            top_k=10000,  # Large number to get all results
            include_metadata=True,
            filter={"project_id": project_id, "type": "UI"}
        )

        # Extract unique files
        files_dict = {}
        for match in results.matches:
            file_id = match.metadata.get("file_id")
            if file_id and file_id not in files_dict:
                files_dict[file_id] = {
                    "file_id": file_id,
                    "filename": match.metadata.get("filename", ""),
                    "file_url": match.metadata.get("file_url", ""),
                    "created_at": match.metadata.get("created_at", ""),
                    "user_id": user_id,
                    "project_id": project_id
                }

        files_list = list(files_dict.values())
        logger.info(f"Found {len(files_list)} files in project {project_id}")
        return files_list

    def delete_file_by_id(self, user_id: str, project_id: str, file_id: str) -> bool:
        """
        Delete all chunks of a specific file.

        Args:
            user_id: User identifier for namespace
            project_id: Project identifier
            file_id: File identifier to delete

        Returns:
            True if deletion was successful
        """
        if not self.index:
            self.create_index_if_not_exists()

        try:
            # Query all chunks for this file
            dummy_embedding = [0.0] * 768  # Zero vector for listing purposes

            results = self.index.query(
                namespace=user_id,
                vector=dummy_embedding,
                top_k=10000,  # Large number to get all chunks
                include_metadata=True,
                filter={"project_id": project_id, "file_id": file_id, "type": "UI"}
            )

            # Extract chunk IDs
            chunk_ids = [match.id for match in results.matches]

            if chunk_ids:
                # Delete all chunks
                self.index.delete(namespace=user_id, ids=chunk_ids)
                logger.info(f"Deleted {len(chunk_ids)} chunks for file {file_id}")
                return True
            else:
                logger.warning(f"No chunks found for file {file_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting file {file_id}: {e}")
            raise

    def get_file_metadata(self, user_id: str, project_id: str, file_id: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific file.

        Args:
            user_id: User identifier for namespace
            project_id: Project identifier
            file_id: File identifier

        Returns:
            File metadata if found, None otherwise
        """
        if not self.index:
            self.create_index_if_not_exists()

        try:
            # Query for any chunk of this file to get metadata
            dummy_embedding = [0.0] * 768  # Zero vector for listing purposes

            results = self.index.query(
                namespace=user_id,
                vector=dummy_embedding,
                top_k=1,  # Just need one chunk to get file metadata
                include_metadata=True,
                filter={"project_id": project_id, "file_id": file_id, "type": "UI"}
            )

            if results.matches:
                match = results.matches[0]
                return {
                    "file_id": file_id,
                    "filename": match.metadata.get("filename", ""),
                    "file_url": match.metadata.get("file_url", ""),
                    "created_at": match.metadata.get("created_at", ""),
                    "user_id": user_id,
                    "project_id": project_id
                }
            else:
                return None

        except Exception as e:
            logger.error(f"Error getting file metadata for {file_id}: {e}")
            raise
