# Vector Database

## Overview
This directory contains **vector database integration** for the DrCode UI Testing backend. It manages vector embeddings for prompt similarity search, document analysis, and AI-powered refinement capabilities.

## Architecture
- **Pinecone Integration**: Primary vector database provider
- **Embedding Management**: Vector generation and storage
- **Similarity Search**: Contextual prompt and document matching
- **Index Management**: Database schema and optimization

## Components

### `pinecone_client.py`
**Purpose**: Pinecone vector database client implementation

**Key Features**:
- **Connection Management**: Handles Pinecone API connections
- **Index Operations**: Create, update, and manage vector indices
- **Vector Storage**: Store document and prompt embeddings
- **Similarity Search**: Query for similar vectors with scoring
- **Batch Operations**: Efficient bulk vector operations

**Core Operations**:
```python
# Initialize client
client = PineconeVectorDB()

# Store vectors
await client.upsert_vectors(vectors, metadata)

# Search similar vectors
results = await client.query_similar(query_vector, top_k=10)

# Manage indices
client.create_index_if_not_exists()
client.delete_index(index_name)
```

## Vector Database Schema

### Prompt Vectors
**Index**: `prompt-embeddings`
**Dimensions**: 1536 (OpenAI ada-002)
**Metadata**:
- `prompt_text`: Original prompt content
- `domain`: Testing domain (web, mobile, api)
- `framework`: Testing framework used
- `quality_score`: Prompt quality rating
- `timestamp`: Creation timestamp

### Document Vectors
**Index**: `document-embeddings`
**Dimensions**: 1536 (OpenAI ada-002)
**Metadata**:
- `document_id`: Unique document identifier
- `document_type`: File type (pdf, docx, txt)
- `content_snippet`: Text excerpt
- `evaluation_score`: Quality assessment score
- `tags`: Document categorization tags

## Use Cases

### 1. Prompt Refinement
- Store historical prompts as vectors
- Find similar prompts for context
- Suggest improvements based on successful patterns
- Track prompt evolution and quality

### 2. Document Analysis
- Embed document content for similarity search
- Compare documents for duplicate detection
- Categorize documents by content similarity
- Enable semantic document retrieval

### 3. Knowledge Base
- Build searchable knowledge repository
- Store testing patterns and solutions
- Enable contextual help and suggestions
- Support AI-powered recommendations

## Configuration

### Environment Variables
```bash
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=drcode-vectors
```

### Index Configuration
```python
index_config = {
    "dimension": 1536,
    "metric": "cosine",
    "pod_type": "p1.x1",
    "replicas": 1
}
```

## Operations

### Vector Generation
```python
# Generate embeddings using OpenAI
embedding = openai.Embedding.create(
    input=text,
    model="text-embedding-ada-002"
)
```

### Similarity Search
```python
# Search for similar vectors
query_results = index.query(
    vector=query_embedding,
    top_k=10,
    include_metadata=True,
    filter={"domain": "web_testing"}
)
```

### Batch Operations
```python
# Bulk upsert for efficiency
vectors_to_upsert = [
    (id, embedding, metadata)
    for id, embedding, metadata in batch_data
]
index.upsert(vectors=vectors_to_upsert)
```

## Performance Optimization
- **Batch Processing**: Group operations for efficiency
- **Metadata Filtering**: Use filters to reduce search space
- **Index Tuning**: Optimize for query patterns
- **Caching**: Cache frequent queries and results
