# Vector Database New Schema Implementation

## Overview

This document describes the new vector database architecture implemented for the DrCode project, featuring a hierarchical user → project → file → chunks structure with namespace-based isolation.

## Architecture

### Hierarchy
```
User → Project → File → Chunks
```

- **Namespace**: `user_id` for complete user isolation
- **Metadata Filtering**: `project_id`, `file_id` for granular querying
- **Chunk-based Storage**: Each file is split into chunks for better retrieval

### Schema Design

Each Pinecone vector record follows this structure:

```json
{
  "id": "file001_chunk_01",
  "values": [/* Gemini embedding array */],
  "metadata": {
    "user_id": "user123",
    "project_id": "projABC", 
    "file_id": "file001",
    "filename": "document.pdf",
    "file_url": "https://storage.drcode.com/user123/projABC/document.pdf",
    "chunk_index": 1,
    "chunk_text": "Some extracted text...",
    "created_at": "2025-07-30T10:00:00Z",
    "type": "UI"
  }
}
```

## Configuration

### Environment Variables
```bash
PINECONE_INDEX_NAME=drcode
PINECONE_API_KEY=your_pinecone_api_key
GEMINI_API_KEY=your_gemini_api_key
```

### Index Configuration
- **Index Name**: `drcode`
- **Dimension**: 768 (Gemini embedding-001 model)
- **Metric**: cosine
- **Namespaces**: Used for user isolation

## API Endpoints

### 1. File Upload with Context
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

file: [file]
user_id: demo_user (optional)
project_id: demo_project (optional)
```

**Response:**
```json
{
  "success": true,
  "message": "File processed and 5 chunks indexed successfully",
  "file_id": "uuid-generated-id",
  "filename": "document.pdf",
  "file_url": "https://storage.drcode.com/demo_user/demo_project/document.pdf",
  "chunks_added": 5,
  "user_id": "demo_user",
  "project_id": "demo_project"
}
```

### 2. Query by Project
```http
POST /api/v1/query/project
Content-Type: application/json

{
  "query": "search text",
  "user_id": "demo_user",
  "project_id": "demo_project", 
  "top_k": 5
}
```

### 3. Query by File
```http
POST /api/v1/query/file
Content-Type: application/json

{
  "query": "search text",
  "file_id": "file-uuid",
  "user_id": "demo_user",
  "project_id": "demo_project",
  "top_k": 5
}
```

## Supported File Types

- **Text Files**: `.txt`, `.md`
- **Documents**: `.docx` (requires python-docx)
- **PDFs**: `.pdf` (requires PyPDF2)

## Key Features

### 1. Namespace Isolation
- Each user has their own namespace in Pinecone
- Complete data isolation between users
- No cross-user data leakage

### 2. Hierarchical Filtering
- Query all files in a project
- Query specific files within a project
- Metadata-based filtering for precise results

### 3. Intelligent Chunking
- Automatic text chunking with overlap
- Sentence and word boundary preservation
- Configurable chunk size and overlap

### 4. File Storage Simulation
- Generates file URLs for storage reference
- Maintains file metadata and relationships
- Ready for integration with actual file storage

## Implementation Details

### Core Classes

#### PineconeVectorDB
- `index_file_chunk()`: Index individual file chunks
- `query_by_project()`: Search within project scope
- `query_by_file()`: Search within specific file
- Uses Gemini embeddings for vector generation

#### VectorDBService
- `upload_file_with_context()`: Process and index files
- `query_project_documents()`: Project-wide search
- `query_file_documents()`: File-specific search
- Handles file type detection and text extraction

### Frontend Components

#### DocumentRAGModal
- File upload with drag-and-drop
- Project and file-specific querying
- Real-time search results display
- Support for multiple file types

## Demo Configuration

For development and testing, the system uses dummy values:
- **User ID**: `demo_user`
- **Project ID**: `demo_project`

These will be replaced with actual authentication values in production.

## Testing

Run the test script to validate functionality:

```bash
cd backend
python test_vector_db_new_schema.py
```

The test covers:
- File upload and chunking
- Project-wide queries
- File-specific queries
- Namespace isolation
- Text chunking functionality

## Migration from Old Schema

The new implementation maintains backward compatibility with existing endpoints:
- `/api/v1/documents/upload` (legacy)
- `/api/v1/search` (legacy)

New endpoints provide enhanced functionality:
- `/api/v1/files/upload` (with context)
- `/api/v1/query/project` (project-scoped)
- `/api/v1/query/file` (file-scoped)

## Future Enhancements

1. **Authentication Integration**: Replace dummy user/project IDs
2. **File Storage**: Implement actual file storage service
3. **Advanced Filtering**: Add date ranges, file types, etc.
4. **Batch Operations**: Support multiple file uploads
5. **Analytics**: Track usage and performance metrics
