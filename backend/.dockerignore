# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test coverage
.coverage
.pytest_cache/
.tox/

# Documentation
*.md
docs/

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Screenshots and test data (exclude from image, mount as volumes if needed)
screenshots/
testcases/
sample_documents.txt
