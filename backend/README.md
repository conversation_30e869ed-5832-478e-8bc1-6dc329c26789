# Backend - DrCode UI Testing API Server

## Overview
The backend component is a **FastAPI-based Python server** that provides AI-powered services for the DrCode UI Testing extension. It handles document evaluation, vector database operations, auto-refinement capabilities, and health monitoring.

## Architecture
- **Framework**: FastAPI with async/await support
- **Language**: Python 3.12+
- **API Design**: RESTful API with OpenAPI documentation
- **CORS**: Configured for cross-origin requests
- **Logging**: Structured logging with configurable levels

## Key Features

### 1. Auto-Refinement Service
- Automatic prompt refinement using vector database
- AI-powered suggestion improvements
- Context-aware refinement algorithms

### 2. Document Evaluation
- Intelligent document analysis and scoring
- Content quality assessment
- Automated evaluation metrics

### 3. Vector Database Management
- Pinecone integration for vector storage
- Similarity search capabilities
- Document embedding management

### 4. Health Monitoring
- API health checks
- Service status monitoring
- Performance metrics

## Project Structure

### `/controllers`
FastAPI route controllers organized by functionality:
- `auto_refinement_controller.py` - Auto-refinement endpoints
- `document_evaluation_controller.py` - Document evaluation endpoints
- `vector_db_controller.py` - Vector database operations
- `health_controller.py` - Health check endpoints

### `/services`
Business logic and service implementations:
- `auto_refinement_service.py` - Core auto-refinement logic
- `document_evaluation_service.py` - Document analysis services
- `vector_db_service.py` - Vector database operations

### `/models`
Pydantic models for request/response schemas

### `/prompt_refinement`
AI prompt refinement algorithms and configurations:
- `analyzer.py` - Prompt analysis logic
- `config.py` - Configuration settings

### `/vector_db`
Vector database client implementations:
- `pinecone_client.py` - Pinecone integration

## Dependencies
- **FastAPI** - Modern web framework
- **Uvicorn** - ASGI server
- **Pydantic** - Data validation
- **Google Generative AI** - Primary AI model integration (Gemini)
- **OpenAI** - Client library for OpenAI-compatible endpoints
- **Pinecone** - Vector database
- **Python-dotenv** - Environment management

## Setup & Usage

### Installation
```bash
pip install -r requirements.txt
```

### Environment Variables
Create a `.env` file with required API keys:
```
GEMINI_API_KEY=your_gemini_api_key
PINECONE_API_KEY=your_pinecone_key
# For backward compatibility (maps to Gemini)
OPENAI_API_KEY=your_gemini_api_key
OPENAI_API_URL=https://generativelanguage.googleapis.com/v1beta/openai
```

### Running the Server
```bash
python main.py
# or
python start_server.py
```

### API Documentation
Once running, access interactive docs at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## API Endpoints

### Health Check
- `GET /api/v1/health` - Service health status

### Auto-Refinement
- `POST /api/v1/auto-refinement/refine` - Refine prompts automatically

### Document Evaluation
- `POST /api/v1/document-evaluation/evaluate` - Evaluate document quality

### Vector Database
- `POST /api/v1/vector-db/store` - Store vectors
- `GET /api/v1/vector-db/search` - Search similar vectors

## Development
- Follows FastAPI best practices
- Async/await for non-blocking operations
- Comprehensive error handling
- Request/response validation with Pydantic
- Structured logging throughout
