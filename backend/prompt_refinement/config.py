"""
Configuration for prompt refinement system
"""

import os
from typing import List


class RefinementConfig:
    """Configuration class for prompt refinement system."""
    
    # Scoring thresholds - Made more aggressive so even good prompts get refined
    MIN_SPECIFICITY_THRESHOLD = float(os.getenv("REFINEMENT_MIN_SPECIFICITY", "0.8"))  # Raised from 0.6 to 0.8
    SPECIFICITY_THRESHOLD = float(os.getenv("REFINEMENT_SPECIFICITY_THRESHOLD", "0.85"))  # Raised from 0.7 to 0.85
    MIN_SIMILARITY_THRESHOLD = float(os.getenv("REFINEMENT_MIN_SIMILARITY", "0.7"))  # Raised from 0.6 to 0.7
    SEMANTIC_SCORE_THRESHOLD = float(os.getenv("REFINEMENT_SEMANTIC_THRESHOLD", "0.75"))  # Raised from 0.6 to 0.75
    MIN_PROMPT_LENGTH = int(os.getenv("REFINEMENT_MIN_PROMPT_LENGTH", "5"))
    
    # Force auto refinement bypass - when True, always refine regardless of thresholds
    FORCE_AUTO_REFINEMENT = os.getenv("FORCE_AUTO_REFINEMENT", "false").lower() == "true"
    
    # Session management
    MAX_ITERATIONS = int(os.getenv("REFINEMENT_MAX_ITERATIONS", "3"))
    SESSION_TIMEOUT_HOURS = int(os.getenv("REFINEMENT_SESSION_TIMEOUT_HOURS", "24"))
    
    # Vector search settings
    VECTOR_SEARCH_TOP_K = int(os.getenv("REFINEMENT_VECTOR_SEARCH_TOP_K", "5"))
    CONTEXT_CHUNKS_FOR_QUESTIONS = int(os.getenv("REFINEMENT_CONTEXT_CHUNKS", "3"))
    
    # Question generation
    MAX_QUESTIONS_PER_ITERATION = int(os.getenv("REFINEMENT_MAX_QUESTIONS", "3"))
    QUESTION_GENERATION_MODEL = os.getenv("REFINEMENT_QUESTION_MODEL", "planner")
    QUESTION_GENERATION_TEMPERATURE = float(os.getenv("REFINEMENT_QUESTION_TEMPERATURE", "0.3"))  # Reduced for consistency

    # Prompt refinement
    PROMPT_REFINEMENT_MODEL = os.getenv("REFINEMENT_PROMPT_MODEL", "planner")
    PROMPT_REFINEMENT_TEMPERATURE = float(os.getenv("REFINEMENT_PROMPT_TEMPERATURE", "0.1"))  # Very low for precise output
    
    # Vague prompt indicators - words that suggest a prompt needs refinement
    VAGUE_INDICATORS = [
        "test", "check", "verify", "make sure", "ensure", "general",
        "basic", "simple", "quick", "help", "assist", "do something",
        "try", "see if", "look at", "examine", "review"
    ]

    # Specific terms - words that suggest a prompt is already detailed
    SPECIFIC_TERMS = [
        "login", "signup", "register", "authenticate", "dashboard",
        "profile", "form", "button", "click", "navigate", "page",
        "download", "upload", "submit", "verify", "validate",
        "api", "endpoint", "integration", "user", "admin", "settings",
        "menu", "search", "filter", "table", "modal", "popup",
        "input", "field", "dropdown", "checkbox", "radio", "select",
        "header", "footer", "sidebar", "content", "section"
    ]
    
    # System prompts for LLM interactions
    QUESTION_GENERATION_SYSTEM_PROMPT = """You are an AI assistant helping to refine user prompts for UI testing automation by AI agents.

Your task is to generate 2-3 practical questions that will help create actionable testing instructions for an AI agent.

FOCUS ON ACTIONABLE DETAILS:
- What specific interactions should the agent perform with the feature?
- How should the agent verify the feature is working correctly?
- What are the expected behaviors or outcomes to test?
- Are there specific data inputs or selections to test with?

QUESTION STYLE:
- Ask about user actions and expected behaviors, not technical details
- Focus on testing scenarios rather than exact element selectors
- Consider common web interaction patterns
- Ask for verification methods that an AI agent can easily check

EXAMPLES OF GOOD QUESTIONS:
- "What specific actions should be performed to test this feature (e.g., clicking, filling forms, making selections)?"
- "What should happen when the feature is used correctly that would indicate it's working?"
- "Are there specific data inputs or scenarios that should be tested?"
- "What visual changes or feedback should appear when the feature is functioning properly?"

EXAMPLES TO AVOID:
- "What is the exact ID of the button?" (too technical)
- "What CSS selector should be used?" (implementation detail)

RESPONSE FORMAT: Return only a JSON array of 2-3 practical questions, nothing else."""

    PROMPT_REFINEMENT_SYSTEM_PROMPT = """You are an AI assistant that refines user prompts for browser automation testing by AI agents.

Your task is to create clean, executable step-by-step instructions that an AI agent can follow to navigate and test websites.

CRITICAL REQUIREMENTS:
1. Create a simple numbered list of actionable steps
2. Use specific element identifiers when provided in the clarifying information
3. When specific elements aren't provided, use reasonable exploratory actions that an AI agent can perform
4. Focus on what the agent should DO, not what information is missing
5. Make each step executable without requiring additional clarification

OUTPUT FORMAT:
```
OBJECTIVE: [Clear, concise goal]

STEPS:
1. [Action the agent should take - be specific when details are available, use exploratory language when they're not]
2. [Next action - focus on common UI patterns and logical progression]
3. [Continue with logical sequence of actions]
4. [Include verification/testing actions where appropriate]
5. [Final verification of the overall objective]
```

INSTRUCTION STYLE:
- Use active, direct language: "Navigate to", "Click", "Enter", "Verify"
- When specific elements are provided, be precise: "Click the Submit button with ID='submit-btn'"
- When specific elements aren't provided, use exploratory language: "Look for and click the main call-to-action button", "Find and interact with the primary feature on the page"
- Focus on common web patterns: buttons, forms, links, navigation menus
- Include logical verification steps: "Verify the page loaded", "Check that the feature responds"

AVOID:
- "NOTE: Details needed" statements
- Mentioning missing information
- Overly complex substep structures
- Prerequisites sections unless truly necessary
- Expected outcome sections that just restate the objective

The output should read like clear instructions an AI agent can immediately execute."""

    @classmethod
    def get_fallback_questions(cls) -> List[str]:
        """Get fallback questions when LLM generation fails."""
        return [
            "What specific actions should be performed to test this feature or functionality?",
            "What should happen when the feature works correctly that would indicate successful operation?",
            "Are there particular data inputs, selections, or scenarios that should be tested?"
        ]
    
    @classmethod
    def get_supported_file_extensions(cls) -> List[str]:
        """Get list of supported document file extensions."""
        return ['.docx', '.pdf', '.txt', '.md']
    
    @classmethod
    def get_document_chunk_settings(cls) -> dict:
        """Get document chunking settings."""
        return {
            'chunk_size': 1000,
            'chunk_overlap': 200,
            'separators': ['\n\n', '\n', '. ', ' ', '']
        }
