# Services

## Overview
This directory contains the **business logic layer** for the DrCode UI Testing backend. Services implement core functionality and are called by controllers to process requests. They handle integration with external APIs, databases, and AI models.

## Architecture
- **Service Layer Pattern**: Separates business logic from HTTP concerns
- **Dependency Injection**: Services are injected into controllers
- **External Integrations**: Manages connections to AI APIs and databases
- **Error Handling**: Comprehensive exception handling and logging

## Services

### `auto_refinement_service.py`
**Purpose**: Core auto-refinement business logic
**Responsibilities**:
- Prompt analysis and improvement
- Vector similarity matching
- AI-powered refinement suggestions
- Context-aware prompt optimization

**Key Features**:
- Integration with Google Gemini models (primary) and other AI providers
- Vector database querying for similar prompts
- Multi-step refinement algorithms
- Performance metrics and logging

### `document_evaluation_service.py`
**Purpose**: Document analysis and quality assessment
**Responsibilities**:
- Document content extraction
- Quality scoring algorithms
- Evaluation metrics calculation
- Result aggregation and reporting

**Key Features**:
- Multi-format document support (PDF, DOCX, TXT)
- AI-powered content analysis
- Configurable evaluation criteria
- Detailed scoring breakdown

### `vector_db_service.py`
**Purpose**: Vector database operations and management
**Responsibilities**:
- Vector embedding generation
- Database storage and retrieval
- Similarity search operations
- Index management and optimization

**Key Features**:
- Pinecone client integration
- Efficient vector operations
- Search result ranking and filtering
- Database health monitoring

## Development Patterns

### Service Initialization
```python
class ExampleService:
    def __init__(self):
        self.client = initialize_external_client()
        self.logger = logging.getLogger(__name__)
```

### Async Operations
```python
async def process_data(self, data: InputModel) -> OutputModel:
    try:
        result = await self.external_api_call(data)
        return self.transform_result(result)
    except Exception as e:
        self.logger.error(f"Processing failed: {e}")
        raise
```

### Error Handling
Services implement comprehensive error handling with proper logging and exception propagation to controllers.

### External Integrations
- **AI APIs**: Google Gemini models (primary), OpenAI compatibility layer
- **Vector Database**: Pinecone for similarity search
- **Document Processing**: Support for multiple file formats
- **Logging**: Structured logging for debugging and monitoring
