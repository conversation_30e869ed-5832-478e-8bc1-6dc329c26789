"""
Browser Automation Service

This service handles communication with the Chrome extension to execute UI test cases.
It uses WebSocket or HTTP communication to trigger test execution through the extension.
"""

import os
import json
import logging
import asyncio
import aiohttp
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

from services.testcase_service import TestCase

logger = logging.getLogger(__name__)


class BrowserAutomationService:
    """Service for executing test cases through Chrome extension"""
    
    def __init__(self):
        """Initialize browser automation service"""
        self.extension_endpoint = "http://localhost:8080/api/execute"  # Chrome extension local server
        self.screenshots_dir = os.path.join(os.path.dirname(__file__), "..", "screenshots")
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # Default timeouts
        self.default_timeout = 30  # seconds
        self.step_timeout = 10     # seconds per step
        
    async def execute_testcase(self, test_case: TestCase, target_url: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute a test case through the Chrome extension
        
        Args:
            test_case: The test case to execute
            target_url: Optional target URL to test against
            
        Returns:
            Dictionary containing execution results
        """
        try:
            start_time = time.time()
            logger.info(f"Executing test case: {test_case.id} - {test_case.name}")
            
            # Prepare execution payload for Chrome extension
            execution_payload = {
                "test_id": test_case.id,
                "test_name": test_case.name,
                "test_description": test_case.description,
                "test_commands": self._create_automation_commands_from_testcase(test_case),
                "target_url": target_url or "about:blank",
                "expected_result": test_case.expected_result,
                "category": test_case.category.value,
                "severity": test_case.severity.value,
                "timeout": self.default_timeout
            }
            
            # Execute the test case based on category
            if test_case.category.value == "security":
                result = await self._execute_security_test(execution_payload)
            elif test_case.category.value == "ui_responsive":
                result = await self._execute_responsive_test(execution_payload)
            elif test_case.category.value == "performance":
                result = await self._execute_performance_test(execution_payload)
            elif test_case.category.value == "accessibility":
                result = await self._execute_accessibility_test(execution_payload)
            elif test_case.category.value == "compatibility":
                result = await self._execute_compatibility_test(execution_payload)
            else:
                result = await self._execute_functional_test(execution_payload)
            
            execution_time = time.time() - start_time
            
            # Take screenshot for evidence
            screenshot_path = await self._take_screenshot(test_case.id)
            
            return {
                "success": True,
                "passed": result.get("passed", False),
                "execution_time": execution_time,
                "screenshot_path": screenshot_path,
                "details": result.get("details", ""),
                "automation_logs": result.get("logs", [])
            }
            
        except Exception as e:
            execution_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"Error executing test case {test_case.id}: {e}")
            
            return {
                "success": False,
                "passed": False,
                "execution_time": execution_time,
                "error_message": str(e),
                "details": f"Test execution failed: {str(e)}"
            }

    def _create_automation_commands_from_testcase(self, test_case: TestCase) -> List[Dict[str, Any]]:
        """
        Create automation commands based on test case description and category
        """
        automation_commands = []
        
        # Create a command based on the test case description and category
        description_lower = test_case.description.lower()
        
        # Add navigation command if URL-related
        if any(word in description_lower for word in ["navigate", "visit", "go to", "open", "load"]):
            automation_commands.append({
                "type": "navigate",
                "description": f"Navigate to test target for {test_case.name}",
                "url": "about:blank",  # Default, will be overridden by target_url
                "timeout": self.step_timeout
            })
        
        # Add interaction commands based on category
        if test_case.category.value == "security":
            # Security tests often involve input validation
            automation_commands.append({
                "type": "security_scan",
                "description": f"Perform security scan for {test_case.name}",
                "test_type": "xss_injection",
                "timeout": self.step_timeout
            })
        elif test_case.category.value == "ui_responsive":
            # UI responsive tests involve viewport changes
            automation_commands.append({
                "type": "viewport_test",
                "description": f"Test UI responsiveness for {test_case.name}",
                "dimensions": [{"width": 1920, "height": 1080}, {"width": 768, "height": 1024}],
                "timeout": self.step_timeout
            })
        elif test_case.category.value == "performance":
            # Performance tests involve timing and load testing
            automation_commands.append({
                "type": "performance_test",
                "description": f"Measure performance for {test_case.name}",
                "metrics": ["load_time", "render_time", "memory_usage"],
                "timeout": self.step_timeout
            })
        elif test_case.category.value == "accessibility":
            # Accessibility tests involve screen reader and keyboard navigation
            automation_commands.append({
                "type": "accessibility_test",
                "description": f"Test accessibility for {test_case.name}",
                "checks": ["keyboard_navigation", "screen_reader", "color_contrast"],
                "timeout": self.step_timeout
            })
        else:
            # Default functional test
            automation_commands.append({
                "type": "functional_test",
                "description": f"Execute functional test: {test_case.name}",
                "test_description": test_case.description,
                "timeout": self.step_timeout
            })
        
        return automation_commands

    def _extract_selector_from_step(self, step: str) -> str:
        """Extract CSS selector from test step"""
        # This is a simplified extraction - in practice, you'd have more sophisticated parsing
        if "button" in step.lower():
            return "button, input[type='button'], input[type='submit']"
        elif "input" in step.lower() or "field" in step.lower():
            return "input, textarea"
        elif "link" in step.lower():
            return "a"
        elif "form" in step.lower():
            return "form"
        else:
            return "*"  # Generic selector

    def _extract_text_from_step(self, step: str) -> str:
        """Extract text to input from test step"""
        # Look for quoted text or specific patterns
        import re
        
        # Find text in quotes
        quoted_text = re.search(r'"([^"]*)"', step)
        if quoted_text:
            return quoted_text.group(1)
        
        # Find text after "with" or "enter"
        with_match = re.search(r'(?:with|enter)\s+([^\s]+)', step, re.IGNORECASE)
        if with_match:
            return with_match.group(1)
        
        # Default test inputs based on step content
        if "long" in step.lower() or "10000" in step:
            return "A" * 10000  # Long string
        elif "special" in step.lower() or "html" in step.lower():
            return "<script>alert('xss')</script>"  # XSS test
        elif "unicode" in step.lower() or "emoji" in step.lower():
            return "🚀 Unicode test 中文 🎉"
        else:
            return "test input"

    def _extract_url_from_step(self, step: str) -> str:
        """Extract URL from navigation step"""
        import re
        
        # Look for URLs in the step
        url_match = re.search(r'https?://[^\s]+', step)
        if url_match:
            return url_match.group(0)
        
        # Default to current page
        return "javascript:void(0)"

    def _extract_wait_time_from_step(self, step: str) -> int:
        """Extract wait time from step"""
        import re
        
        # Look for numbers followed by seconds/ms
        time_match = re.search(r'(\d+)\s*(?:second|sec|ms)', step, re.IGNORECASE)
        if time_match:
            return int(time_match.group(1))
        
        return 2  # Default 2 seconds

    def _extract_scroll_direction_from_step(self, step: str) -> str:
        """Extract scroll direction from step"""
        if "down" in step.lower():
            return "down"
        elif "up" in step.lower():
            return "up"
        elif "left" in step.lower():
            return "left"
        elif "right" in step.lower():
            return "right"
        else:
            return "down"

    def _extract_dimensions_from_step(self, step: str) -> Dict[str, int]:
        """Extract dimensions from resize step"""
        import re
        
        # Look for dimensions like 320x480 or 320px
        dimension_match = re.search(r'(\d+)(?:x|\s*×\s*)(\d+)', step)
        if dimension_match:
            return {
                "width": int(dimension_match.group(1)),
                "height": int(dimension_match.group(2))
            }
        
        # Look for specific device names
        if "mobile" in step.lower() or "320" in step:
            return {"width": 320, "height": 568}
        elif "tablet" in step.lower() or "768" in step:
            return {"width": 768, "height": 1024}
        elif "desktop" in step.lower() or "1920" in step:
            return {"width": 1920, "height": 1080}
        
        return {"width": 1366, "height": 768}  # Default

    async def _execute_functional_test(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute functional test"""
        try:
            await asyncio.sleep(2)  # Simulate test execution time
            
            # Simulate test results based on test content
            passed = self._simulate_test_result(payload)
            
            return {
                "passed": passed,
                "details": f"Functional test {'passed' if passed else 'failed'}",
                "logs": [f"Executed {len(payload['test_commands'])} commands"]
            }
            
        except Exception as e:
            logger.error(f"Error in functional test execution: {e}")
            return {
                "passed": False,
                "details": f"Functional test failed: {str(e)}",
                "logs": [f"Error: {str(e)}"]
            }

    async def _execute_security_test(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute security-focused test"""
        try:
            await asyncio.sleep(3)  # Security tests take longer
            
            # Security tests are more likely to find issues
            passed = self._simulate_test_result(payload, failure_rate=0.7)
            
            security_details = []
            if not passed:
                security_details = [
                    "XSS vulnerability detected in input field",
                    "Input validation bypassed",
                    "Potential injection point found"
                ]
            
            return {
                "passed": passed,
                "details": f"Security test {'passed' if passed else 'failed'}: {'; '.join(security_details) if not passed else 'No vulnerabilities found'}",
                "logs": [f"Security scan completed", f"Tested {len(payload['test_commands'])} attack vectors"]
            }
            
        except Exception as e:
            return {
                "passed": False,
                "details": f"Security test error: {str(e)}",
                "logs": [f"Security test failed: {str(e)}"]
            }

    async def _execute_responsive_test(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute responsive design test"""
        try:
            await asyncio.sleep(4)  # Responsive tests need multiple viewport checks
            
            passed = self._simulate_test_result(payload, failure_rate=0.5)
            
            responsive_details = []
            if not passed:
                responsive_details = [
                    "Layout breaks at mobile viewport",
                    "Elements overlap at zoom level 200%",
                    "Horizontal scrollbar appears at tablet size"
                ]
            
            return {
                "passed": passed,
                "details": f"Responsive test {'passed' if passed else 'failed'}: {'; '.join(responsive_details) if not passed else 'All viewports render correctly'}",
                "logs": [
                    "Tested mobile viewport (320px)",
                    "Tested tablet viewport (768px)", 
                    "Tested desktop viewport (1920px)",
                    "Tested zoom levels 50%-300%"
                ]
            }
            
        except Exception as e:
            return {
                "passed": False,
                "details": f"Responsive test error: {str(e)}",
                "logs": [f"Responsive test failed: {str(e)}"]
            }

    async def _execute_performance_test(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute performance test"""
        try:
            await asyncio.sleep(5)  # Performance tests take longer
            
            passed = self._simulate_test_result(payload, failure_rate=0.6)
            
            performance_details = []
            if not passed:
                performance_details = [
                    "Page load time exceeds 3 seconds",
                    "Memory usage increases continuously",
                    "UI becomes unresponsive under load"
                ]
            
            return {
                "passed": passed,
                "details": f"Performance test {'passed' if passed else 'failed'}: {'; '.join(performance_details) if not passed else 'Performance within acceptable limits'}",
                "logs": [
                    "Measured page load time",
                    "Monitored memory usage",
                    "Tested UI responsiveness under load",
                    "Simulated slow network conditions"
                ]
            }
            
        except Exception as e:
            return {
                "passed": False,
                "details": f"Performance test error: {str(e)}",
                "logs": [f"Performance test failed: {str(e)}"]
            }

    async def _execute_accessibility_test(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute accessibility test"""
        try:
            await asyncio.sleep(3)
            
            passed = self._simulate_test_result(payload, failure_rate=0.4)
            
            accessibility_details = []
            if not passed:
                accessibility_details = [
                    "Missing ARIA labels on interactive elements",
                    "Insufficient color contrast ratio",
                    "Cannot navigate using keyboard only"
                ]
            
            return {
                "passed": passed,
                "details": f"Accessibility test {'passed' if passed else 'failed'}: {'; '.join(accessibility_details) if not passed else 'All accessibility checks passed'}",
                "logs": [
                    "Tested keyboard navigation",
                    "Checked ARIA labels",
                    "Verified color contrast",
                    "Tested screen reader compatibility"
                ]
            }
            
        except Exception as e:
            return {
                "passed": False,
                "details": f"Accessibility test error: {str(e)}",
                "logs": [f"Accessibility test failed: {str(e)}"]
            }

    async def _execute_compatibility_test(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute browser compatibility test"""
        try:
            await asyncio.sleep(4)
            
            passed = self._simulate_test_result(payload, failure_rate=0.3)
            
            compatibility_details = []
            if not passed:
                compatibility_details = [
                    "JavaScript errors in older browsers",
                    "CSS features not supported",
                    "Different behavior across browsers"
                ]
            
            return {
                "passed": passed,
                "details": f"Compatibility test {'passed' if passed else 'failed'}: {'; '.join(compatibility_details) if not passed else 'Compatible across all target browsers'}",
                "logs": [
                    "Tested Chrome compatibility",
                    "Tested Firefox compatibility",
                    "Tested Safari compatibility",
                    "Tested with JavaScript disabled"
                ]
            }
            
        except Exception as e:
            return {
                "passed": False,
                "details": f"Compatibility test error: {str(e)}",
                "logs": [f"Compatibility test failed: {str(e)}"]
            }

    def _simulate_test_result(self, payload: Dict[str, Any], failure_rate: float = 0.3) -> bool:
        """
        Simulate test results for demonstration purposes
        In a real implementation, this would analyze actual browser automation results
        """
        import random
        
        # Higher severity tests are more likely to fail
        severity = payload.get("severity", "medium")
        if severity == "critical":
            failure_rate *= 1.5
        elif severity == "high":
            failure_rate *= 1.2
        elif severity == "low":
            failure_rate *= 0.7
        
        # Random result based on failure rate
        return random.random() > failure_rate

    async def _take_screenshot(self, test_id: str) -> str:
        """Take screenshot for test evidence"""
        try:
            # Simulate screenshot capture
            screenshot_filename = f"screenshot_{test_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            screenshot_path = os.path.join(self.screenshots_dir, screenshot_filename)
            
            # In real implementation, this would capture actual screenshot via Chrome extension
            # For now, create a placeholder file
            with open(screenshot_path, 'w') as f:
                f.write(f"Screenshot placeholder for test {test_id}")
            
            return screenshot_path
            
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return None

    async def send_command_to_extension(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send command to Chrome extension for execution
        This would be the actual communication layer with the extension
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.extension_endpoint,
                    json=command,
                    timeout=aiohttp.ClientTimeout(total=self.default_timeout)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {
                            "success": False,
                            "error": f"Extension responded with status {response.status}"
                        }
                        
        except Exception as e:
            logger.error(f"Error communicating with extension: {e}")
            return {
                "success": False,
                "error": f"Failed to communicate with extension: {str(e)}"
            }

    async def check_extension_health(self) -> bool:
        """Check if Chrome extension is available and responsive"""
        try:
            health_command = {"type": "health_check"}
            result = await self.send_command_to_extension(health_command)
            return result.get("success", False)
            
        except Exception as e:
            logger.error(f"Extension health check failed: {e}")
            return False
