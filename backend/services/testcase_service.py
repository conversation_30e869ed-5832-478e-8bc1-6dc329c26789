"""
UI Testcase Generation Service

This service generates comprehensive UI test cases based on user prompts
and identifies potential breaking scenarios for UI testing.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
import openai
from dotenv import load_dotenv
from pydantic import BaseModel

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class TestStatus(str, Enum):
    """Test execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    ERROR = "error"
    SKIPPED = "skipped"


class TestSeverity(str, Enum):
    """Test severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class TestCase(BaseModel):
    """Test case model"""
    id: str
    name: str
    description: str
    category: str
    severity: TestSeverity
    expected_result: str
    breaking_scenario: str
    user_prompt: str
    status: TestStatus = TestStatus.PENDING
    execution_time: Optional[float] = None
    error_message: Optional[str] = None
    screenshot_path: Optional[str] = None
    created_at: datetime
    executed_at: Optional[datetime] = None


class TestcaseService:
    """Service for generating and managing UI test cases"""
    
    def __init__(self):
        """Initialize the testcase service"""
        # Initialize client to use the local proxy endpoint
        self.openai_client = openai.OpenAI(
            api_key=os.getenv("OPENAI_API_KEY", "dummy_key"),  # Use dummy key for local proxy
            base_url=os.getenv("OPENAI_API_URL", "https://api.openai.com/v1/")
        )
        
        # Create testcases storage directory
        self.storage_dir = os.path.join(os.path.dirname(__file__), "..", "testcases")
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # Test case generation prompts
        self.test_generation_prompt = """
        You are an expert QA engineer specializing in UI testing. Given a user prompt describing a UI interaction or feature, 
        generate {generate_count} comprehensive test cases that could potentially break the UI. Think critically about edge cases, 
        boundary conditions, and scenarios that commonly cause UI failures.

        Focus on these breaking scenarios:
        1. Input validation failures (invalid data, XSS attempts, SQL injection)
        2. State synchronization problems (stale data, lost updates)
        3. File upload/download edge cases (large files, unsupported formats)
        4. Permission and authorization errors (unauthorized access, privilege escalation)
        5. Data persistence and rollback failures (unsaved changes, failed transactions)

        For each test case, provide:
        - A clear, descriptive name
        - Detailed description of what could break
        - Category (functional, accessibility, security, edge_case)
        - Severity (critical, high, medium, low)
        - Expected result that would indicate failure
        - Specific breaking scenario explanation

        User Prompt: {user_prompt}

        Generate exactly {generate_count} test cases in JSON format with this structure:
        {{
            "test_cases": [
                {{
                    "name": "Test case name",
                    "description": "What this test verifies and what could break",
                    "category": "category_name",
                    "severity": "severity_level",
                    "expected_result": "What should happen vs what indicates failure",
                    "breaking_scenario": "Specific explanation of how this could break the UI"
                }}
            ]
        }}
        """

    async def generate_testcases(self, user_prompt: str) -> List[TestCase]:
        """
        Generate UI test cases based on user prompt
        
        Args:
            user_prompt: User's description of UI functionality to test
            
        Returns:
            List of generated test cases
        """
        try:
            logger.info(f"Generating test cases for prompt: {user_prompt}")
            
            prompt = self.test_generation_prompt.format(user_prompt=user_prompt, generate_count=10)
            model = os.getenv("TESTCASE_GENERATOR_MODEL", "planner")  # Use proxy model name
            
            # Call Gemini via OpenAI-compatible endpoint to generate test cases
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert QA engineer specializing in breaking UI applications through comprehensive testing."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.1,
            )
            
            # Parse the response
            try:
                # Handle potential null response from Gemini API
                response_content_raw = response.choices[0].message.content
                if response_content_raw is None:
                    logger.error("Received null response from Gemini API")
                    return self._generate_fallback_testcases(user_prompt)
                
                response_content = response_content_raw.strip()
                
                # Extract JSON from response
                if "```json" in response_content:
                    json_start = response_content.find("```json") + 7
                    json_end = response_content.find("```", json_start)
                    json_content = response_content[json_start:json_end].strip()
                else:
                    json_content = response_content
                
                test_data = json.loads(json_content)
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.error(f"Response content: {response_content}")
                # Fallback to manual generation
                return self._generate_fallback_testcases(user_prompt)
            
            # Convert to TestCase objects
            test_cases = []
            for i, test_data_item in enumerate(test_data.get("test_cases", [])):
                test_case = TestCase(
                    id=f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i+1}",
                    name=test_data_item.get("name", f"Test Case {i+1}"),
                    description=test_data_item.get("description", "") + "\n\n" + user_prompt,
                    category=test_data_item.get("category", "functional"),
                    severity=TestSeverity(test_data_item.get("severity", "medium")),
                    expected_result=test_data_item.get("expected_result", ""),
                    breaking_scenario=test_data_item.get("breaking_scenario", ""),
                    user_prompt=user_prompt,
                    created_at=datetime.now()
                )
                test_cases.append(test_case)
            
            # Save test cases to storage
            await self._save_testcases(test_cases)
            
            logger.info(f"Generated {len(test_cases)} test cases")
            return test_cases
            
        except Exception as e:
            logger.error(f"Error generating test cases: {e}")
            # Return fallback test cases
            return self._generate_fallback_testcases(user_prompt)

    def _generate_fallback_testcases(self, user_prompt: str) -> List[TestCase]:
        """Generate fallback test cases if AI generation fails"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        fallback_tests = [
            {
                "name": "fallback test case",
                "description": "Default test case",
                "category": "security",
                "severity": TestSeverity.CRITICAL,
                "expected_result": "UI should handle invalid inputs gracefully without breaking",
                "breaking_scenario": "Unvalidated inputs could cause XSS attacks, UI layout breaks, or application crashes"
            }
        ]
        
        test_cases = []
        for i, test_data in enumerate(fallback_tests):
            test_case = TestCase(
                id=f"fallback_{timestamp}_{i+1}",
                name=test_data["name"],
                description=test_data["description"],
                category=test_data["category"],
                severity=test_data["severity"],
                expected_result=test_data["expected_result"],
                breaking_scenario=test_data["breaking_scenario"],
                user_prompt=user_prompt,
                created_at=datetime.now()
            )
            test_cases.append(test_case)
        
        return test_cases

    async def _save_testcases(self, test_cases: List[TestCase]) -> None:
        """Save test cases to storage"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"testcases_{timestamp}.json"
            filepath = os.path.join(self.storage_dir, filename)
            
            # Convert to dict for JSON serialization
            test_cases_data = {
                "timestamp": timestamp,
                "test_cases": [test_case.dict() for test_case in test_cases]
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_cases_data, f, indent=2, default=str)
            
            logger.info(f"Saved test cases to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving test cases: {e}")

    async def get_all_testcases(self) -> List[TestCase]:
        """Get all stored test cases"""
        try:
            test_cases = []
            
            for filename in os.listdir(self.storage_dir):
                if filename.startswith("testcases_") and filename.endswith(".json"):
                    filepath = os.path.join(self.storage_dir, filename)
                    
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        
                    for test_data in data.get("test_cases", []):
                        # Handle datetime parsing
                        test_data["created_at"] = datetime.fromisoformat(test_data["created_at"].replace("Z", "+00:00")) if isinstance(test_data["created_at"], str) else test_data["created_at"]
                        if test_data.get("executed_at"):
                            test_data["executed_at"] = datetime.fromisoformat(test_data["executed_at"].replace("Z", "+00:00")) if isinstance(test_data["executed_at"], str) else test_data["executed_at"]
                        
                        test_case = TestCase(**test_data)
                        test_cases.append(test_case)
            
            # Sort by creation time (newest first)
            test_cases.sort(key=lambda x: x.created_at, reverse=True)
            return test_cases
            
        except Exception as e:
            logger.error(f"Error retrieving test cases: {e}")
            return []

    async def update_testcase_status(self, test_id: str, status: TestStatus, 
                                   execution_time: Optional[float] = None,
                                   error_message: Optional[str] = None,
                                   screenshot_path: Optional[str] = None) -> bool:
        """Update test case execution status"""
        try:
            # Find and update the test case
            for filename in os.listdir(self.storage_dir):
                if filename.startswith("testcases_") and filename.endswith(".json"):
                    filepath = os.path.join(self.storage_dir, filename)
                    
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Look for the test case to update
                    updated = False
                    for test_data in data.get("test_cases", []):
                        if test_data.get("id") == test_id:
                            test_data["status"] = status.value
                            test_data["executed_at"] = datetime.now().isoformat()
                            if execution_time is not None:
                                test_data["execution_time"] = execution_time
                            if error_message:
                                test_data["error_message"] = error_message
                            if screenshot_path:
                                test_data["screenshot_path"] = screenshot_path
                            updated = True
                            break
                    
                    if updated:
                        # Save updated data
                        with open(filepath, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2, default=str)
                        
                        logger.info(f"Updated test case {test_id} status to {status}")
                        return True
            
            logger.warning(f"Test case {test_id} not found for status update")
            return False
            
        except Exception as e:
            logger.error(f"Error updating test case status: {e}")
            return False

    async def get_testcase_by_id(self, test_id: str) -> Optional[TestCase]:
        """Get a specific test case by ID"""
        try:
            all_testcases = await self.get_all_testcases()
            for test_case in all_testcases:
                if test_case.id == test_id:
                    return test_case
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving test case {test_id}: {e}")
            return None

    async def get_testcases_summary(self) -> Dict[str, Any]:
        """Get summary statistics of all test cases"""
        try:
            all_testcases = await self.get_all_testcases()
            
            summary = {
                "total_testcases": len(all_testcases),
                "status_breakdown": {status.value: 0 for status in TestStatus},
                "severity_breakdown": {severity.value: 0 for severity in TestSeverity},
                "category_breakdown": {},
                "recent_executions": []
            }
            
            for test_case in all_testcases:
                summary["status_breakdown"][test_case.status.value] += 1
                summary["severity_breakdown"][test_case.severity.value] += 1
                summary["category_breakdown"][test_case.category] += 1
                
                if test_case.executed_at:
                    summary["recent_executions"].append({
                        "id": test_case.id,
                        "name": test_case.name,
                        "status": test_case.status.value,
                        "executed_at": test_case.executed_at.isoformat(),
                        "execution_time": test_case.execution_time
                    })
            
            # Sort recent executions by execution time
            summary["recent_executions"].sort(key=lambda x: x["executed_at"], reverse=True)
            summary["recent_executions"] = summary["recent_executions"][:10]  # Last 10
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating test cases summary: {e}")
            return {
                "total_testcases": 0,
                "status_breakdown": {status.value: 0 for status in TestStatus},
                "severity_breakdown": {severity.value: 0 for severity in TestSeverity},
                "category_breakdown": {},
                "recent_executions": []
            }
