"""
Document Evaluation Service using Gemini models via OpenAI-compatible API
"""

import os
import json
import logging
from typing import Dict, Any
from openai import OpenAI
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)


class DocumentEvaluationService:
    """Service for evaluating documents using Gemini models via OpenAI-compatible endpoint"""
    
    def __init__(self):
        # Initialize client for Gemini via OpenAI-compatible endpoint
        api_key = os.getenv("OPENAI_API_KEY")  # This contains the Gemini API key
        api_url = os.getenv("OPENAI_API_URL", "https://generativelanguage.googleapis.com/v1beta/openai")

        if not api_key:
            logger.warning("OPENAI_API_KEY (Gemini API key) not found in environment variables")
            self.client = None
        else:
            self.client = OpenAI(api_key=api_key, base_url=api_url)
    
    def evaluate_document(self, document_text: str) -> Dict[str, Any]:
        """
        Evaluate a document based on the 5 criteria for autonomous UI testing
        
        Args:
            document_text: The document content to evaluate
            
        Returns:
            Dict containing the evaluation results in JSON format
        """
        if not self.client:
            return {
                "error": "OpenAI client not initialized",
                "details": "Please set OPENAI_API_KEY environment variable and restart the server"
            }
        
        # Validate input
        if not document_text or not document_text.strip():
            return {
                "error": "Document text is empty or invalid",
                "details": "Please provide valid document content for evaluation"
            }
        
        # Construct the evaluation prompt
        prompt = self._build_evaluation_prompt(document_text)

        try:
            model = os.getenv("DOCUMENT_EVALUATION_MODEL", "gemini-2.5-flash")
            logger.info(f"Making API call to Gemini with model: {model}")
            logger.info(f"API URL: {os.getenv('OPENAI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/openai')}")

            response = self.client.chat.completions.create(
                model=model,  # Using Gemini model via OpenAI-compatible endpoint
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert technical evaluator for autonomous UI testing systems. You must return only valid JSON, no additional text, no markdown formatting, no code blocks, and no explanation. Return raw JSON only."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1
            )

            logger.info(f"API response received: {response}")
            logger.info(f"Response choices: {len(response.choices) if response.choices else 0}")
            
            # Extract and parse the response - handle potential null content
            if not response.choices or len(response.choices) == 0:
                logger.error("No choices in API response")
                return {
                    "error": "No choices in API response",
                    "details": "The API response contained no choices"
                }

            response_content = response.choices[0].message.content
            logger.info(f"Response content: {response_content}")

            if response_content is None:
                logger.error("Received null response from Gemini API")
                logger.error(f"Full response object: {response}")
                logger.error(f"Message object: {response.choices[0].message}")
                return {
                    "error": "Null response from Gemini API",
                    "details": "The API returned an empty response. This might be due to content filtering or an invalid model name."
                }
            
            response_text = response_content.strip()
            
            # Clean up markdown formatting if present
            if response_text.startswith('```json'):
                # Remove markdown code block formatting
                response_text = response_text.replace('```json', '').replace('```', '').strip()
            elif response_text.startswith('```'):
                # Remove any other code block formatting
                response_text = response_text.replace('```', '').strip()
            
            # Try to parse as JSON
            try:
                evaluation_result = json.loads(response_text)
                return evaluation_result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Gemini response as JSON: {e}")
                logger.error(f"Response text: {response_text}")
                # Return a fallback response
                return {
                    "error": "Failed to parse evaluation results",
                    "raw_response": response_text
                }
                
        except Exception as e:
            logger.error(f"Error calling Gemini API: {str(e)}")
            # Return a proper error response instead of raising exception
            return {
                "error": f"Gemini API error: {str(e)}",
                "details": "Please check your Gemini API key and internet connection"
            }
    
    def _build_evaluation_prompt(self, document_text: str) -> str:
        """
        Build the evaluation prompt for the document
        
        Args:
            document_text: The document content to evaluate
            
        Returns:
            The formatted prompt string
        """
        prompt = f"""You are an expert technical evaluator for autonomous UI testing systems.

Given the following product documentation delimited by triple backticks, analyze the quality and completeness of the document with respect to enabling an AI agent to autonomously test the UI of the described website.

```{document_text}```

---

Your task is to evaluate each *user flow* described in the document along the following 5 dimensions, using the weights provided. Use strict, calibrated scoring that reflects real-world agent performance:

Evaluation Criteria:

1. UI Element Mapping (30 points)  
   How clearly and consistently UI elements are described (e.g., buttons, input fields, labels, dropdowns).  
   Assess clarity, naming consistency, uniqueness of selectors, and specificity of elements mentioned.

2. Flow Coverage (30 points)  
   How comprehensively each user flow is described.  
   Include complete sequences, decision branches, edge cases, and failure modes where applicable.

3. Action-Intent Mapping (20 points)  
   Whether user actions are linked to their functional or business intent.  
   Example: "Click 'Submit' to finalize the order and trigger backend validation."

4. Structure & Consistency (15 points)  
   Whether the document is well-structured, chunkable, and consistently formatted.  
   Look for headings, numbered steps, and section clarity.

5. Ambiguity / Noise (5 points)  
   Evaluate if the document avoids vague language, contradictions, or irrelevant details.  
   High score = precise, unambiguous, and task-relevant.

---

Scoring Calibration:

Use a strict and consistent scale aligned with real-world success rates of autonomous agents:

- 60–70 → Bare minimum quality needed for agent to complete the flows reliably.  
- 80 → High quality. Most edge cases, flows, and UI states are navigable.  
- 90+ → Exceptional quality. Full coverage, no ambiguity, highly actionable.  
- <60 → Insufficient for reliable automation. Missing or vague information.

Apply the entire score range where appropriate. Avoid inflation.

A user flow is a sequence of user actions toward a defined goal (e.g., “Login”, “Search”, “Checkout”). Use section headers or task groupings to infer flows. Maintain flow order from the document.

- Each category is scored out of its corresponding weight:
  - UI Element Mapping: 30
  - Flow Coverage: 30
  - Action-Intent Mapping: 20
  - Structure: 15
  - Ambiguity / Noise: 5
- `score` is the total of the above for each flow.
- `overall_score` is the average across all flow scores.
- Recommendations should be specific and actionable.

Only return the JSON. No explanation outside of it.

---

Output Format:

Return only a JSON object in the following format:

```json
{{
  "overall_score": 74.3,
  "flow_scores": [
    {{
      "flow_name": "Login Flow",
      "score": 70,
      "ui_element_mapping": 21,
      "flow_coverage": 26,
      "action_intent_mapping": 14,
      "structure": 8,
      "ambiguity_noise": 1,
      "recommendations": [
        "Clarify what happens after login success.",
        "Include error state for invalid credentials."
      ]
    }},
    {{
      "flow_name": "Checkout Flow",
      "score": 78,
      "ui_element_mapping": 25,
      "flow_coverage": 23,
      "action_intent_mapping": 16,
      "structure": 10,
      "ambiguity_noise": 4,
      "recommendations": [
        "Define what triggers the payment modal.",
        "Use consistent button labels across steps."
      ]
    }}
  ],
  "global_recommendations": [
    "Include edge cases and alternate paths where relevant.",
    "Use consistent, unique identifiers for UI elements.",
    "Convert paragraphs into clearly numbered step-by-step instructions."
  ]
}}
```

NOTE: The sample JSON above is only a formatting reference. Do not reuse the numerical values or recommendations — generate all content based on the input document provided.

Only return valid JSON with no text before or after it."""

        return prompt
