# UI Testcase Generator

A comprehensive AI-powered system for generating UI test cases that can potentially break web applications. This system uses critical thinking to identify edge cases, security vulnerabilities, performance issues, and accessibility violations.

## 🚀 Features

### Intelligent Test Case Generation
- **AI-Powered Analysis**: Uses Gemini 2.5-Pro to analyze UI descriptions and generate comprehensive test cases
- **Critical Thinking**: Focuses on scenarios that commonly break UI applications
- **Multiple Categories**: Covers functional, security, performance, accessibility, and compatibility testing
- **Severity Assessment**: Prioritizes test cases by critical, high, medium, and low severity

### Test Categories
1. **Security Testing**: XSS, SQL injection, input validation, authentication bypass
2. **UI Responsiveness**: Mobile compatibility, zoom levels, viewport handling
3. **Performance Testing**: Memory leaks, load testing, slow network conditions
4. **Accessibility Testing**: Keyboard navigation, screen readers, color contrast
5. **Compatibility Testing**: Cross-browser support, feature detection
6. **Functional Testing**: Core user flows and interactions
7. **Edge Cases**: Boundary conditions, error scenarios

### Browser Automation
- **Chrome Extension Integration**: Automated test execution through extension
- **Real Browser Testing**: Tests run in actual browser environment
- **Screenshot Capture**: Visual evidence of test results
- **Detailed Logging**: Comprehensive execution logs for debugging

## 🏗️ Architecture

```
Backend (Python/FastAPI)
├── Testcase Service       # AI-powered test generation
├── Browser Automation     # Extension communication
├── Storage Management     # Test case persistence
└── REST API              # Web interface

Chrome Extension (JavaScript)
├── Test Executor         # Browser automation
├── DOM Manipulation      # UI interaction
├── Performance Monitor   # Metrics collection
└── Screenshot Capture    # Visual evidence

Web Interface (HTML/JS)
├── Test Generation UI    # User input interface
├── Test Management       # View and organize tests
├── Execution Control     # Run tests
└── Results Dashboard     # Test outcomes
```

## 🛠️ Installation

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
python main.py
```

### Chrome Extension Setup
```bash
cd chrome-extension
npm install
npm run build
# Load unpacked extension in Chrome
```

### Environment Variables
Create a `.env` file in the backend directory:
```env
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_URL=https://api.openai.com/v1
PINECONE_API_KEY=your_pinecone_key
PINECONE_ENVIRONMENT=your_pinecone_env
```

## 📖 Usage

### 1. Generate Test Cases

#### Using the Web Interface
1. Open `backend/ui_testcase_generator.html` in your browser
2. Describe your UI functionality
3. Click "Generate Test Cases"
4. Review the generated test cases

#### Using the API
```python
import requests

response = requests.post('http://localhost:8001/api/v1/testcases/generate', 
    json={
        'user_prompt': 'A login form with username, password, and remember me checkbox',
        'generate_count': 5
    }
)
test_cases = response.json()
```

#### Using the Demo Script
```bash
cd backend
python demo_testcase_generator.py
```

### 2. Execute Test Cases

#### Through Web Interface
1. Select test cases to execute
2. Click "Execute Selected Test Cases"
3. Monitor execution status
4. Review results with screenshots

#### Through API
```python
requests.post('http://localhost:8001/api/v1/testcases/execute',
    json={
        'test_ids': ['test_20240101_120000_1', 'test_20240101_120000_2'],
        'target_url': 'https://example.com'
    }
)
```

### 3. View Results
```python
# Get all test cases with status
response = requests.get('http://localhost:8001/api/v1/testcases/')
all_tests = response.json()

# Get execution summary
response = requests.get('http://localhost:8001/api/v1/testcases/summary/stats')
summary = response.json()
```

## 🎯 Sample Test Case

```json
{
  "name": "Input Validation Stress Test",
  "description": "Test UI with malicious and edge case inputs",
  "category": "security",
  "severity": "critical",
  "steps": [
    "Input extremely long strings (>10000 characters)",
    "Input special characters and HTML/JS code",
    "Input null, undefined, and empty values",
    "Test with unicode and emoji characters"
  ],
  "expected_result": "UI should handle invalid inputs gracefully without breaking",
  "breaking_scenario": "Unvalidated inputs could cause XSS attacks, UI layout breaks, or application crashes"
}
```

## 🔧 API Endpoints

### Test Case Management
- `POST /api/v1/testcases/generate` - Generate new test cases
- `GET /api/v1/testcases/` - Get all test cases
- `GET /api/v1/testcases/{id}` - Get specific test case
- `GET /api/v1/testcases/summary/stats` - Get execution statistics

### Test Execution
- `POST /api/v1/testcases/execute` - Execute multiple test cases
- `POST /api/v1/testcases/run-single/{id}` - Execute single test case
- `PUT /api/v1/testcases/status` - Update test case status

## 🧪 Example Scenarios

### E-commerce Product Page
```
"A product page with images, price, quantity selector, add to cart button, 
reviews section, and related products. Users can select variants and add to cart."
```

**Generated Tests:**
- Security: XSS in product reviews, price manipulation
- Performance: Large image loading, infinite scroll
- Accessibility: Screen reader compatibility, keyboard navigation
- Responsive: Mobile cart interactions, zoom levels

### User Registration Form
```
"Registration form with email, password, confirm password, terms checkbox. 
Form validates inputs and creates user account."
```

**Generated Tests:**
- Security: SQL injection, password strength bypass
- Functional: Email validation, password matching
- Accessibility: Form labels, error messaging
- Performance: Validation performance with long inputs

## 🔍 Critical Breaking Scenarios

### Input Validation Failures
- XSS: `<script>alert('xss')</script>`
- SQL Injection: `'; DROP TABLE users; --`
- Buffer Overflow: 10,000+ character strings
- Unicode Issues: Mixed language characters and emojis

### UI Responsiveness Issues
- Mobile viewports (320px width)
- Extreme zoom levels (300%+)
- Orientation changes
- Dynamic content loading

### Performance Bottlenecks
- Memory leaks with large datasets
- Slow network conditions (3G simulation)
- Heavy DOM manipulation
- Infinite scroll performance

### Accessibility Violations
- Missing keyboard navigation
- Poor color contrast (< 4.5:1 ratio)
- Missing ARIA labels
- Screen reader incompatibility

## 🚨 Security Considerations

### Responsible Testing
- Only test applications you own or have permission to test
- Use test environments, not production systems
- Be cautious with XSS and injection payloads
- Respect rate limits and terms of service

### Data Protection
- Test cases are stored locally in JSON files
- Screenshots are saved in backend/screenshots/
- No sensitive data is sent to external services (except OpenAI for generation)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Write tests for new functionality
5. Submit a pull request

### Areas for Contribution
- Additional test case categories
- More sophisticated browser automation
- Advanced accessibility testing
- Performance metrics collection
- Visual regression testing

## 📊 Metrics and Reporting

The system tracks:
- Test execution times
- Pass/fail rates by category
- Common failure patterns
- Performance metrics
- Screenshot evidence

## 🔮 Future Enhancements

### Planned Features
- Visual regression testing
- AI-powered test result analysis
- Cross-browser execution
- CI/CD integration
- Test case recommendations based on code changes

### Advanced Automation
- Machine learning for test case optimization
- Automatic bug report generation
- Integration with bug tracking systems
- Continuous monitoring and alerting

## 📚 References

- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)
- [Web Content Accessibility Guidelines (WCAG)](https://www.w3.org/WAG/WCAG21/quickref/)
- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙋‍♀️ Support

For questions, issues, or feature requests:
1. Check the existing issues on GitHub
2. Create a new issue with detailed information
3. Join our community discussions

---

**Built with ❤️ for better UI testing and quality assurance**
