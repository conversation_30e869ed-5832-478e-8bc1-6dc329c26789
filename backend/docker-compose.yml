services:
  nanobrowser-backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '8001:8001'
    environment:
      # Set these in your .env file or pass them directly
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_URL=${OPENAI_API_URL}
      - PINECONE_API_KEY=${PINECONE_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - PINECONE_INDEX_NAME=${PINECONE_INDEX_NAME}
      - PINECONE_ENVIRONMENT=${PINECONE_ENVIRONMENT}
      - LLM_PROXY_API_KEY=${LLM_PROXY_API_KEY}
      - DEFAULT_PLAN_MODEL=${DEFAULT_PLAN_MODEL:-gemini-2.5-flash}
      - DEFAULT_NAVIGATION_MODEL=${DEFAULT_NAVIGATION_MODEL:-gemini-2.0-flash}
      - DEFAULT_VALIDATION_MODEL=${DEFAULT_VALIDATION_MODEL:-gemini-2.5-flash}
      - TESTCASE_GENERATOR_MODEL=${TESTCASE_GENERATOR_MODEL:-models/gemini-2.5-flash}
    volumes:
      # Mount volumes for persistent data
      - ./screenshots:/app/screenshots
      - ./testcases:/app/testcases
      - ./vector_db:/app/vector_db
      # Mount .env file if it exists
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8001/api/v1/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
