# Models

## Overview
This directory contains **Pydantic data models** used throughout the DrCode UI Testing backend for request/response validation, data serialization, and type safety.

## Purpose
- **Data Validation**: Ensure incoming requests meet expected schemas
- **Response Serialization**: Structure API responses consistently
- **Type Safety**: Provide compile-time type checking with Python typing
- **Documentation**: Auto-generate OpenAPI/Swagger documentation

## Model Categories

### Request Models
Models for validating incoming API requests:
- Input validation with field constraints
- Required/optional field definitions
- Data type enforcement
- Custom validators for complex fields

### Response Models
Models for structuring API responses:
- Consistent response formats
- Nested object serialization
- Optional field handling
- Status and metadata inclusion

### Internal Models
Models for internal data structures:
- Service layer data transfer objects
- Database entity representations
- Configuration objects
- Intermediate processing results

## Development Patterns

### Basic Model Structure
```python
from pydantic import BaseModel, Field
from typing import Optional

class ExampleModel(BaseModel):
    required_field: str = Field(..., description="Required field")
    optional_field: Optional[str] = Field(None, description="Optional field")
    validated_field: int = Field(gt=0, le=100, description="Integer between 1-100")
```

### Custom Validators
```python
from pydantic import validator

class ModelWithValidation(BaseModel):
    email: str
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValueError('Invalid email format')
        return v
```

### Configuration
```python
class ExampleModel(BaseModel):
    field: str
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "field": "example_value"
            }
        }
```

## Benefits
- **API Documentation**: Automatic OpenAPI schema generation
- **Runtime Validation**: Catches invalid data at API boundaries
- **IDE Support**: Full autocomplete and type checking
- **Serialization**: JSON serialization/deserialization
- **Error Messages**: Detailed validation error responses
