<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>UI Testcase Generator</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }
      textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        min-height: 120px;
        resize: vertical;
      }
      textarea:focus {
        border-color: #4caf50;
        outline: none;
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      button:hover {
        background-color: #45a049;
      }
      button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
      .execute-btn {
        background-color: #2196f3;
      }
      .execute-btn:hover {
        background-color: #1976d2;
      }
      .loading {
        text-align: center;
        color: #666;
        font-style: italic;
      }
      .testcase {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 15px;
        background-color: #fafafa;
      }
      .testcase h3 {
        margin-top: 0;
        color: #333;
      }
      .testcase-meta {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;
        flex-wrap: wrap;
      }
      .badge {
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
      }
      .badge-critical {
        background-color: #f44336;
        color: white;
      }
      .badge-high {
        background-color: #ff9800;
        color: white;
      }
      .badge-medium {
        background-color: #ffeb3b;
        color: black;
      }
      .badge-low {
        background-color: #4caf50;
        color: white;
      }
      .badge-pending {
        background-color: #9e9e9e;
        color: white;
      }
      .badge-running {
        background-color: #2196f3;
        color: white;
      }
      .badge-passed {
        background-color: #4caf50;
        color: white;
      }
      .badge-failed {
        background-color: #f44336;
        color: white;
      }
      .badge-error {
        background-color: #9c27b0;
        color: white;
      }
      .steps {
        margin: 15px 0;
      }
      .steps ol {
        padding-left: 20px;
      }
      .steps li {
        margin-bottom: 5px;
        line-height: 1.4;
      }
      .breaking-scenario {
        background-color: #fff3cd;
        border: 1px solid #ffeaa8;
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
      }
      .breaking-scenario strong {
        color: #856404;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }
      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .error {
        background-color: #ffebee;
        border: 1px solid #ffcdd2;
        color: #c62828;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }
      .success {
        background-color: #e8f5e8;
        border: 1px solid #c8e6c9;
        color: #2e7d32;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }
      .checkbox-container {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .checkbox-container input {
        margin-right: 10px;
        transform: scale(1.2);
      }
      .selected-count {
        color: #666;
        font-size: 14px;
        margin-left: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🚀 UI Testcase Generator</h1>
      <p style="text-align: center; color: #666; margin-bottom: 30px">
        Generate comprehensive UI test cases that can potentially break your application. Critical thinking applied to
        find edge cases, security vulnerabilities, and performance issues.
      </p>

      <div class="form-group">
        <label for="userPrompt">Describe the UI functionality you want to test:</label>
        <textarea
          id="userPrompt"
          placeholder="Example: A login form with username and password fields, remember me checkbox, and social login options. Users should be able to authenticate and access their dashboard..."></textarea>
      </div>

      <button onclick="generateTestcases()" id="generateBtn"> 🔍 Generate Test Cases </button>

      <button onclick="loadExistingTestcases()" id="loadBtn"> 📋 Load Existing Test Cases </button>
    </div>

    <!-- Statistics Container -->
    <div id="statsContainer" style="display: none"></div>

    <!-- Test Cases Container -->
    <div id="testcasesContainer"></div>

    <!-- Execution Controls -->
    <div id="executionControls" style="display: none" class="container">
      <h3>Execute Test Cases</h3>
      <div class="checkbox-container">
        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" />
        <label for="selectAll">Select All Test Cases</label>
        <span class="selected-count" id="selectedCount">0 selected</span>
      </div>
      <div style="margin-top: 15px">
        <button onclick="executeSelectedTestcases()" id="executeBtn" class="execute-btn">
          ▶️ Execute Selected Test Cases
        </button>
        <button onclick="refreshTestcases()" id="refreshBtn"> 🔄 Refresh Status </button>
      </div>
    </div>

    <script>
      const API_BASE = 'http://localhost:8001/api/v1';
      let allTestcases = [];

      async function generateTestcases() {
        const userPrompt = document.getElementById('userPrompt').value.trim();
        if (!userPrompt) {
          alert('Please enter a description of the UI functionality to test.');
          return;
        }

        const generateBtn = document.getElementById('generateBtn');
        const originalText = generateBtn.textContent;
        generateBtn.textContent = '🔄 Generating...';
        generateBtn.disabled = true;

        try {
          const response = await fetch(`${API_BASE}/testcases/generate`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_prompt: userPrompt,
              generate_count: 5,
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const testcases = await response.json();
          allTestcases = testcases;
          displayTestcases(testcases);
          showExecutionControls();
          showSuccessMessage(`✅ Generated ${testcases.length} test cases successfully!`);
        } catch (error) {
          console.error('Error generating test cases:', error);
          showErrorMessage(`❌ Error generating test cases: ${error.message}`);
        } finally {
          generateBtn.textContent = originalText;
          generateBtn.disabled = false;
        }
      }

      async function loadExistingTestcases() {
        const loadBtn = document.getElementById('loadBtn');
        const originalText = loadBtn.textContent;
        loadBtn.textContent = '🔄 Loading...';
        loadBtn.disabled = true;

        try {
          const [testcasesResponse, statsResponse] = await Promise.all([
            fetch(`${API_BASE}/testcases/`),
            fetch(`${API_BASE}/testcases/summary/stats`),
          ]);

          if (!testcasesResponse.ok || !statsResponse.ok) {
            throw new Error('Failed to load data');
          }

          const testcases = await testcasesResponse.json();
          const stats = await statsResponse.json();

          allTestcases = testcases;
          displayTestcases(testcases);
          displayStats(stats);
          showExecutionControls();

          if (testcases.length === 0) {
            showErrorMessage('📝 No test cases found. Generate some test cases first.');
          } else {
            showSuccessMessage(`📋 Loaded ${testcases.length} existing test cases.`);
          }
        } catch (error) {
          console.error('Error loading test cases:', error);
          showErrorMessage(`❌ Error loading test cases: ${error.message}`);
        } finally {
          loadBtn.textContent = originalText;
          loadBtn.disabled = false;
        }
      }

      function displayStats(stats) {
        const statsContainer = document.getElementById('statsContainer');
        statsContainer.innerHTML = `
                <div class="container">
                    <h3>📊 Test Cases Statistics</h3>
                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-number">${stats.total_testcases}</div>
                            <div>Total Test Cases</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.status_breakdown.passed}</div>
                            <div>Passed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.status_breakdown.failed}</div>
                            <div>Failed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.status_breakdown.pending}</div>
                            <div>Pending</div>
                        </div>
                    </div>
                </div>
            `;
        statsContainer.style.display = 'block';
      }

      function displayTestcases(testcases) {
        const container = document.getElementById('testcasesContainer');

        if (testcases.length === 0) {
          container.innerHTML = '<div class="container"><p>No test cases to display.</p></div>';
          return;
        }

        const testcasesHtml = testcases
          .map(
            testcase => `
                <div class="container">
                    <div class="testcase">
                        <div class="checkbox-container">
                            <input type="checkbox" id="testcase_${testcase.id}" value="${testcase.id}" onchange="updateSelectedCount()">
                            <h3>${testcase.name}</h3>
                        </div>
                        
                        <div class="testcase-meta">
                            <span class="badge badge-${testcase.severity}">${testcase.severity}</span>
                            <span class="badge badge-${testcase.status}">${testcase.status}</span>
                            <span class="badge">
                                ${testcase.category}
                            </span>
                        </div>
                        
                        <p><strong>Description:</strong> ${testcase.description}</p>
                        
                        <!-- Removed steps section -->
                        
                        <p><strong>Expected Result:</strong> ${testcase.expected_result}</p>
                        
                        <div class="breaking-scenario">
                            <strong>💥 Breaking Scenario:</strong> ${testcase.breaking_scenario}
                        </div>
                        
                        ${
                          testcase.executed_at
                            ? `
                            <div style="margin-top: 15px; color: #666; font-size: 14px;">
                                <strong>Executed:</strong> ${new Date(testcase.executed_at).toLocaleString()}
                                ${testcase.execution_time ? `(${testcase.execution_time.toFixed(2)}s)` : ''}
                                ${testcase.error_message ? `<br><span style="color: #d32f2f;">Error: ${testcase.error_message}</span>` : ''}
                            </div>
                        `
                            : ''
                        }
                        
                        <div style="margin-top: 15px;">
                            <button onclick="executeSingleTestcase('${testcase.id}')" class="execute-btn">
                                ▶️ Run This Test
                            </button>
                        </div>
                    </div>
                </div>
            `,
          )
          .join('');

        container.innerHTML = testcasesHtml;
      }

      function showExecutionControls() {
        document.getElementById('executionControls').style.display = 'block';
        updateSelectedCount();
      }

      function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');

        checkboxes.forEach(checkbox => {
          checkbox.checked = selectAll.checked;
        });

        updateSelectedCount();
      }

      function updateSelectedCount() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][value]:checked');
        const count = checkboxes.length;
        document.getElementById('selectedCount').textContent = `${count} selected`;

        const executeBtn = document.getElementById('executeBtn');
        executeBtn.disabled = count === 0;
      }

      async function executeSelectedTestcases() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][value]:checked');
        const testIds = Array.from(checkboxes).map(cb => cb.value);

        if (testIds.length === 0) {
          alert('Please select at least one test case to execute.');
          return;
        }

        const executeBtn = document.getElementById('executeBtn');
        const originalText = executeBtn.textContent;
        executeBtn.textContent = '🔄 Executing...';
        executeBtn.disabled = true;

        try {
          const response = await fetch(`${API_BASE}/testcases/execute`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              test_ids: testIds,
              target_url: null,
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          showSuccessMessage(
            `🚀 Started execution of ${testIds.length} test cases. They will run in the background through the Chrome extension.`,
          );

          // Auto-refresh after a few seconds
          setTimeout(() => {
            refreshTestcases();
          }, 5000);
        } catch (error) {
          console.error('Error executing test cases:', error);
          showErrorMessage(`❌ Error executing test cases: ${error.message}`);
        } finally {
          executeBtn.textContent = originalText;
          executeBtn.disabled = false;
        }
      }

      async function executeSingleTestcase(testId) {
        try {
          const response = await fetch(`${API_BASE}/testcases/run-single/${testId}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          showSuccessMessage(`🚀 Started execution of test case: ${result.test_name}`);

          // Auto-refresh after a few seconds
          setTimeout(() => {
            refreshTestcases();
          }, 3000);
        } catch (error) {
          console.error('Error executing test case:', error);
          showErrorMessage(`❌ Error executing test case: ${error.message}`);
        }
      }

      async function refreshTestcases() {
        if (allTestcases.length > 0) {
          await loadExistingTestcases();
        }
      }

      function showErrorMessage(message) {
        const container = document.getElementById('testcasesContainer');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = message;
        container.insertBefore(errorDiv, container.firstChild);

        setTimeout(() => {
          errorDiv.remove();
        }, 5000);
      }

      function showSuccessMessage(message) {
        const container = document.getElementById('testcasesContainer');
        const successDiv = document.createElement('div');
        successDiv.className = 'success';
        successDiv.textContent = message;
        container.insertBefore(successDiv, container.firstChild);

        setTimeout(() => {
          successDiv.remove();
        }, 5000);
      }

      // Load existing test cases on page load
      window.addEventListener('load', () => {
        loadExistingTestcases();
      });
    </script>
  </body>
</html>
