#!/usr/bin/env python3
"""
UI Testcase Generator Demo

This script demonstrates how to use the UI testcase generator to create
comprehensive test cases that can break UI applications.
"""

import json
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.testcase_service import TestcaseService


async def demo_testcase_generation():
    """Demonstrate testcase generation with sample prompts"""
    
    print("🚀 UI Testcase Generator Demo")
    print("="*50)
    
    # Initialize service
    testcase_service = TestcaseService()
    
    # Sample prompts to demonstrate different scenarios
    sample_prompts = [
        {
            "name": "E-commerce Product Page",
            "prompt": "A product page for an online store with product images, price, quantity selector, add to cart button, reviews section, and related products. Users should be able to select product variants, add items to cart, and read reviews."
        },
        {
            "name": "User Registration Form",
            "prompt": "A user registration form with fields for email, password, confirm password, first name, last name, phone number, and terms acceptance checkbox. Form should validate inputs and create user account."
        },
        {
            "name": "Social Media Feed",
            "prompt": "A social media feed showing posts with images, text, likes, comments, and share buttons. Users can scroll infinitely, like posts, add comments, and share content. Real-time updates should show new posts."
        },
        {
            "name": "Online Banking Dashboard",
            "prompt": "A banking dashboard showing account balances, recent transactions, quick transfer options, and bill payment features. Users should be able to view transaction history, transfer money between accounts, and pay bills securely."
        },
        {
            "name": "Video Streaming Interface",
            "prompt": "A video streaming interface with video player, playlist, search functionality, user profiles, and recommendation system. Users can play videos, create playlists, search content, and get personalized recommendations."
        }
    ]
    
    all_generated_testcases = []
    
    for i, sample in enumerate(sample_prompts, 1):
        print(f"\n{i}. Generating test cases for: {sample['name']}")
        print("-" * 40)
        print(f"Prompt: {sample['prompt'][:100]}...")
        
        try:
            # Generate test cases
            testcases = await testcase_service.generate_testcases(sample['prompt'])
            
            print(f"✅ Generated {len(testcases)} test cases")
            
            # Display test cases
            for j, testcase in enumerate(testcases, 1):
                print(f"\n  Test Case {j}: {testcase.name}")
                print(f"  Category: {testcase.category.value} | Severity: {testcase.severity.value}")
                print(f"  Breaking Scenario: {testcase.breaking_scenario[:100]}...")
                print(f"  Description: {testcase.description[:100]}...")
            
            all_generated_testcases.extend(testcases)
            
        except Exception as e:
            print(f"❌ Error generating test cases: {e}")
    
    print(f"\n🎉 Demo completed! Generated {len(all_generated_testcases)} total test cases")
    
    # Show summary statistics
    await show_testcase_summary(testcase_service)
    
    return all_generated_testcases


async def show_testcase_summary(testcase_service):
    """Show summary of all test cases"""
    
    print("\n📊 Test Cases Summary")
    print("="*30)
    
    try:
        summary = await testcase_service.get_testcases_summary()
        
        print(f"Total Test Cases: {summary['total_testcases']}")
        
        print("\nBy Status:")
        for status, count in summary['status_breakdown'].items():
            if count > 0:
                print(f"  {status.title()}: {count}")
        
        print("\nBy Severity:")
        for severity, count in summary['severity_breakdown'].items():
            if count > 0:
                print(f"  {severity.title()}: {count}")
        
        print("\nBy Category:")
        for category, count in summary['category_breakdown'].items():
            if count > 0:
                print(f"  {category.replace('_', ' ').title()}: {count}")
                
    except Exception as e:
        print(f"❌ Error getting summary: {e}")


def demonstrate_critical_test_scenarios():
    """Show examples of critical test scenarios that can break UIs"""
    
    print("\n🔥 Critical UI Breaking Scenarios")
    print("="*40)
    
    critical_scenarios = [
        {
            "category": "Input Validation",
            "scenarios": [
                "XSS injection through form fields",
                "SQL injection in search inputs",
                "Buffer overflow with extremely long strings",
                "Unicode and emoji handling",
                "Null byte injection"
            ]
        },
        {
            "category": "UI Responsiveness",
            "scenarios": [
                "Layout breaks on mobile devices",
                "Element overlap at different zoom levels",
                "Content overflow in small viewports",
                "Touch target size on mobile",
                "Orientation change handling"
            ]
        },
        {
            "category": "Performance Issues",
            "scenarios": [
                "Memory leaks with large datasets",
                "UI freezing under heavy load",
                "Slow rendering with complex layouts",
                "Network timeout handling",
                "Infinite scroll performance"
            ]
        },
        {
            "category": "Accessibility Violations",
            "scenarios": [
                "Missing keyboard navigation",
                "Poor color contrast ratios",
                "Missing screen reader support",
                "Inaccessible form controls",
                "Missing ARIA labels"
            ]
        },
        {
            "category": "Security Vulnerabilities",
            "scenarios": [
                "CSRF token bypass",
                "Session fixation attacks",
                "Insecure direct object references",
                "Authentication bypass",
                "Data exposure in client-side code"
            ]
        }
    ]
    
    for category_info in critical_scenarios:
        print(f"\n🎯 {category_info['category']}:")
        for scenario in category_info['scenarios']:
            print(f"   • {scenario}")


async def interactive_demo():
    """Interactive demo where user can input their own prompts"""
    
    print("\n🎮 Interactive Testcase Generation")
    print("="*40)
    print("Enter your own UI description to generate test cases!")
    print("(Press Ctrl+C to exit)")
    
    testcase_service = TestcaseService()
    
    try:
        while True:
            print("\n" + "-"*50)
            user_prompt = input("📝 Describe your UI functionality: ").strip()
            
            if not user_prompt:
                print("Please enter a valid description.")
                continue
            
            if user_prompt.lower() in ['exit', 'quit', 'q']:
                break
            
            print("\n🔄 Generating test cases...")
            
            try:
                testcases = await testcase_service.generate_testcases(user_prompt)
                
                print(f"✅ Generated {len(testcases)} test cases:\n")
                
                for i, testcase in enumerate(testcases, 1):
                    print(f"{i}. {testcase.name}")
                    print(f"   Category: {testcase.category.value}")
                    print(f"   Severity: {testcase.severity.value}")
                    print(f"   Breaking Scenario: {testcase.breaking_scenario}")
                    print(f"   Steps: {len(testcase.steps)} test steps")
                    print()
                
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except KeyboardInterrupt:
        print("\n\n👋 Demo ended. Thanks for trying the UI Testcase Generator!")


async def main():
    """Main demo function"""
    
    print("Welcome to the UI Testcase Generator!")
    print("This tool generates comprehensive test cases designed to break UI applications.")
    print("It uses AI to think critically about edge cases, security vulnerabilities, and performance issues.\n")
    
    mode = input("Choose demo mode:\n1. Auto Demo (sample prompts)\n2. Interactive Demo (your prompts)\n3. Show Critical Scenarios\n4. All of the above\nEnter choice (1-4): ").strip()
    
    if mode == "1" or mode == "4":
        await demo_testcase_generation()
    
    if mode == "2" or mode == "4":
        await interactive_demo()
    
    if mode == "3" or mode == "4":
        demonstrate_critical_test_scenarios()
    
    print("\n🎯 Next Steps:")
    print("1. Start the backend server: cd backend && python main.py")
    print("2. Open ui_testcase_generator.html in your browser")
    print("3. Install the Chrome extension for automated test execution")
    print("4. Generate test cases and run them against your UI")
    
    print("\n💡 Pro Tips:")
    print("• Be specific in your UI descriptions for better test cases")
    print("• Focus on critical user flows and edge cases")
    print("• Run tests in different browsers and devices")
    print("• Use the Chrome extension for automated execution")


if __name__ == "__main__":
    asyncio.run(main())
