#!/usr/bin/env python3
"""
Advanced Pinecone Inspector
Interactive script with more detailed analysis options
"""

import os
from pinecone import Pinecone
from dotenv import load_dotenv
import json
from datetime import datetime

# Load environment variables
load_dotenv()

class PineconeInspector:
    def __init__(self):
        self.api_key = os.getenv("PINECONE_API_KEY")
        if not self.api_key:
            raise ValueError("PINECONE_API_KEY not found in environment")
        
        self.pc = Pinecone(api_key=self.api_key)
        print(f"✅ Connected to Pinecone with key: {self.api_key[:8]}...{self.api_key[-4:]}")
    
    def list_all_indexes(self):
        """List all indexes with basic info"""
        indexes = self.pc.list_indexes()
        
        if not indexes:
            print("No indexes found")
            return []
        
        print(f"\n📋 Found {len(indexes)} index(es):")
        for i, idx in enumerate(indexes, 1):
            print(f"{i}. {idx.name}")
            print(f"   Dimension: {idx.dimension}, Metric: {idx.metric}")
            print(f"   Status: {idx.status}")
        
        return indexes
    
    def get_detailed_stats(self, index_name):
        """Get detailed statistics for a specific index"""
        try:
            index = self.pc.Index(index_name)
            stats = index.describe_index_stats()
            
            print(f"\n📊 DETAILED STATS for '{index_name}':")
            print(f"   Total Vectors: {stats.get('total_vector_count', 0):,}")
            print(f"   Index Fullness: {stats.get('index_fullness', 0):.4%}")
            print(f"   Dimension: {stats.get('dimension', 'N/A')}")
            
            if 'namespaces' in stats and stats['namespaces']:
                print(f"   \n🏷️  NAMESPACE BREAKDOWN:")
                for ns_name, ns_stats in stats['namespaces'].items():
                    display_name = ns_name if ns_name else "(default)"
                    print(f"      {display_name}: {ns_stats.get('vector_count', 0):,} vectors")
            
            # Raw stats for debugging
            print(f"\n🔍 RAW STATISTICS:")
            print(json.dumps(stats, indent=2, default=str))
            
        except Exception as e:
            print(f"❌ Error getting stats for {index_name}: {e}")
    
    def sample_vectors(self, index_name, count=5):
        """Get sample vectors from an index (if any exist)"""
        try:
            index = self.pc.Index(index_name)
            
            # Try to fetch some vectors (this is tricky since we need IDs)
            stats = index.describe_index_stats()
            total_vectors = stats.get('total_vector_count', 0)
            
            print(f"\n🔍 SAMPLE VECTOR INFO for '{index_name}':")
            print(f"   Cannot directly sample without vector IDs")
            print(f"   Total vectors available: {total_vectors:,}")
            
            if total_vectors > 0:
                print(f"   To view specific vectors, you would need their IDs")
                print(f"   Vector dimension: {stats.get('dimension', 'N/A')}")
        
        except Exception as e:
            print(f"❌ Error sampling vectors: {e}")
    
    def check_index_health(self, index_name):
        """Check if index is healthy and ready"""
        try:
            # Get index description
            index_desc = self.pc.describe_index(index_name)
            
            print(f"\n🏥 HEALTH CHECK for '{index_name}':")
            print(f"   Status: {index_desc.status}")
            print(f"   Ready: {'✅' if index_desc.status.get('ready') else '❌'}")
            print(f"   State: {index_desc.status.get('state', 'Unknown')}")
            print(f"   Host: {index_desc.host}")
            
            # Try a simple operation
            index = self.pc.Index(index_name)
            stats = index.describe_index_stats()
            print(f"   API Response: ✅ Working")
            
        except Exception as e:
            print(f"❌ Health check failed: {e}")
    
    def interactive_menu(self):
        """Interactive menu for exploring indexes"""
        while True:
            print("\n" + "="*50)
            print("🔍 PINECONE INSPECTOR MENU")
            print("="*50)
            print("1. List all indexes")
            print("2. Get detailed stats for an index")
            print("3. Check index health")
            print("4. Sample vector info")
            print("5. Show account summary")
            print("0. Exit")
            
            choice = input("\nEnter your choice (0-5): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                self.list_all_indexes()
            elif choice == "2":
                index_name = input("Enter index name: ").strip()
                if index_name:
                    self.get_detailed_stats(index_name)
            elif choice == "3":
                index_name = input("Enter index name: ").strip()
                if index_name:
                    self.check_index_health(index_name)
            elif choice == "4":
                index_name = input("Enter index name: ").strip()
                if index_name:
                    self.sample_vectors(index_name)
            elif choice == "5":
                self.show_account_summary()
            else:
                print("❌ Invalid choice. Please try again.")
    
    def show_account_summary(self):
        """Show overall account summary"""
        try:
            indexes = self.pc.list_indexes()
            
            print(f"\n📊 ACCOUNT SUMMARY:")
            print(f"   API Key: {self.api_key[:8]}...{self.api_key[-4:]}")
            print(f"   Total Indexes: {len(indexes)}")
            
            total_vectors = 0
            total_storage = 0
            
            for idx in indexes:
                try:
                    index = self.pc.Index(idx.name)
                    stats = index.describe_index_stats()
                    vectors = stats.get('total_vector_count', 0)
                    total_vectors += vectors
                    
                    # Estimate storage (768 dimensions * 4 bytes per float * vector count)
                    estimated_bytes = vectors * 768 * 4
                    total_storage += estimated_bytes
                    
                except:
                    pass
            
            print(f"   Total Vectors: {total_vectors:,}")
            print(f"   Estimated Storage: {total_storage / (1024*1024):.2f} MB")
            print(f"   Report Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ Error getting account summary: {e}")

def main():
    try:
        inspector = PineconeInspector()
        
        # Show quick overview first
        indexes = inspector.list_all_indexes()
        
        if indexes:
            print(f"\n💡 TIP: Your current index is '{indexes[0].name}'")
            print("This is where your uploaded documents are stored as vectors.")
        
        # Start interactive menu
        inspector.interactive_menu()
        
    except Exception as e:
        print(f"❌ Failed to initialize inspector: {e}")

if __name__ == "__main__":
    main()
