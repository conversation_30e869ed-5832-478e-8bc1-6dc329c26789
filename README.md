# DrCode UI Testing Extension

## Overview
DrCode UI Testing is an AI-powered UI testing and automation tool designed as a Chrome extension. This project enables automated testing and evaluation of web interfaces using artificial intelligence to streamline the testing process.

## Project Structure
This is a monorepo managed with **pnpm workspaces** and **Turbo** for efficient build orchestration. The project consists of multiple packages and applications working together to provide comprehensive UI testing capabilities.

### Key Technologies
- **TypeScript** - Primary language for type-safe development
- **React** - UI framework for extension interfaces
- **Vite** - Build tool for fast development and production builds
- **Turbo** - Monorepo orchestration and caching
- **pnpm** - Package manager with workspace support
- **Python** - Backend services and AI processing
- **Tailwind CSS** - Utility-first CSS framework

## Main Components

### `/backend` 
Python-based backend services providing AI-powered document evaluation, vector database operations, and auto-refinement capabilities.

### `/chrome-extension`
Core Chrome extension manifest and configuration files.

### `/packages`
Shared utility packages including:
- Development utilities
- Hot module replacement
- Internationalization
- Schema validation
- UI components
- Storage management
- Build configurations

### `/pages`
Extension UI pages including content scripts, options page, and side panel interface.

## Development

### Prerequisites
- Node.js >= 22.12.0
- pnpm >= 9.15.1
- Python 3.12+ (for backend services)

### Setup
```bash
pnpm install
```

### Development
```bash
pnpm dev          # Chrome development
pnpm dev:firefox  # Firefox development
```

### Building
```bash
pnpm build        # Chrome build
pnpm build:firefox # Firefox build
```

### Testing
```bash
pnpm e2e          # End-to-end tests
pnpm type-check   # TypeScript checking
pnpm lint         # Linting
```

## License
Apache-2.0

## Repository
https://github.com/airia-in/ui-testing-extension
