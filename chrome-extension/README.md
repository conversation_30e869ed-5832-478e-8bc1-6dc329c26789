# Chrome Extension

## Overview
This directory contains the **core Chrome extension configuration** for DrCode UI Testing. It manages the extension manifest, build configuration, and essential settings that define how the extension operates within the browser environment.

## Architecture
- **Manifest V3**: Modern Chrome extension architecture
- **Multi-Browser Support**: Chrome, Firefox, and Opera compatibility
- **TypeScript**: Type-safe development with modern JavaScript features
- **Vite Build System**: Fast development and production builds

## Key Files

### `manifest.js`
**Purpose**: Dynamic manifest generation with cross-browser support

**Key Features**:
- **Dynamic Configuration**: Generates manifest based on environment variables
- **Cross-Browser Compatibility**: Handles Chrome, Firefox, and Opera differences
- **Side Panel Support**: Chrome side panel integration (disabled for Firefox)
- **Opera Sidebar**: Opera-specific sidebar action support
- **Permissions Management**: Comprehensive permission declarations

**Browser-Specific Adaptations**:
```javascript
// Chrome: Uses sidePanel API
side_panel: {
  default_path: 'side-panel/index.html'
}

// Opera: Uses sidebar_action API
sidebar_action: {
  default_panel: 'side-panel/index.html',
  default_title: 'DrCode: UI Testing'
}

// Firefox: No sidebar support (falls back to popup)
```

### `package.json`
**Purpose**: Extension-specific dependencies and build configuration

**Key Dependencies**:
- **LangChain Integrations**: Multiple AI provider support
  - `@langchain/openai`: OpenAI integration
  - `@langchain/anthropic`: Claude integration
  - `@langchain/google-genai`: Google AI integration
  - `@langchain/groq`: Groq API integration
  - `@langchain/ollama`: Local AI model support
- **Workspace Packages**: Internal package dependencies
- **JSON Repair**: Data parsing and validation utilities

### `tsconfig.json`
**Purpose**: TypeScript configuration for extension development
- Type checking configuration
- Module resolution settings
- Chrome extension type declarations

### `vite.config.mts`
**Purpose**: Vite build configuration
- Development and production build settings
- Asset handling and optimization
- Environment-specific configurations

## Extension Permissions

### Core Permissions
- **`storage`**: Extension storage for settings and data
- **`scripting`**: Dynamic script injection capabilities
- **`tabs`**: Tab access and manipulation
- **`activeTab`**: Current tab interaction
- **`debugger`**: Advanced debugging capabilities
- **`unlimitedStorage`**: Large data storage support

### Host Permissions
- **`<all_urls>`**: Access to all websites for testing
- **Content Script Injection**: Automatic script injection on all sites

## Extension Components

### Background Service Worker
- **File**: `background.iife.js`
- **Type**: Module-based service worker
- **Purpose**: Extension lifecycle management, API handling

### Content Scripts
- **File**: `content/index.iife.js`
- **Matches**: All HTTP/HTTPS URLs
- **Purpose**: DOM manipulation, page interaction, testing execution

### Extension Pages
- **Options Page**: `options/index.html` - Extension settings and configuration
- **Side Panel**: `side-panel/index.html` - Main testing interface
- **Popup**: Extension popup interface

### Web Accessible Resources
- JavaScript files for injected functionality
- CSS files for styling
- Icons and images
- Permission handling pages

## Build Process

### Development Build
```bash
pnpm dev  # Development build with hot reload
```

### Production Build
```bash
pnpm build  # Optimized production build
```

### Environment Variables
- **`__DEV__`**: Development mode flag
- **`__FIREFOX__`**: Firefox-specific build
- **`__OPERA__`**: Opera-specific build

## Multi-Browser Strategy

### Chrome (Primary Target)
- Full feature support including side panel
- Modern Manifest V3 APIs
- Advanced debugging capabilities

### Firefox Compatibility
- Removes unsupported features (side panel)
- Uses compatible API alternatives
- Maintains core functionality

### Opera Support
- Uses sidebar_action for Opera-specific features
- Maintains Chrome Web Store compatibility
- Enhanced sidebar experience

## AI Integration
The extension integrates with multiple AI providers through LangChain, with primary focus on Gemini:
- **Google Gemini**: Primary AI provider for test generation and analysis (gemini-2.5-pro, gemini-2.5-flash)
- **Anthropic**: Claude for code analysis
- **Groq**: Fast inference capabilities
- **Ollama**: Local AI model support
- **OpenAI**: Legacy support via Gemini proxy

This multi-provider approach ensures reliability while prioritizing Gemini for optimal performance and cost efficiency.
