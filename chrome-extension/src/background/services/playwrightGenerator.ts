import { logger } from '../log';
import { actionTracker } from './actionTracker';
import { playwrightStorage } from '@extension/storage';

export interface PlaywrightExportOptions {
  language?: 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
  includeComments?: boolean;
  includeAssertions?: boolean;
  includeWaitFor?: boolean;
  filename?: string;
}

export class PlaywrightGenerator {
  /**
   * Generate a Playwright script from tracked actions
   */
  static async generateScript(options: PlaywrightExportOptions = {}): Promise<string> {
    const defaultOptions: PlaywrightExportOptions = {
      language: 'javascript',
      includeComments: true,
      includeAssertions: true,
      includeWaitFor: true,
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Save script options to storage
    await playwrightStorage.setScriptOptions({
      language: finalOptions.language!,
      includeComments: finalOptions.includeComments!,
      includeAssertions: finalOptions.includeAssertions!,
      includeWaitFor: finalOptions.includeWaitFor!,
    });

    return await actionTracker.generatePlaywrightScript({
      language: finalOptions.language!,
      includeComments: finalOptions.includeComments!,
      includeAssertions: finalOptions.includeAssertions!,
      includeWaitFor: finalOptions.includeWaitFor!,
    });
  }

  /**
   * Export tracked actions to a downloadable file
   */
  static exportToFile(options: PlaywrightExportOptions = {}): void {
    const defaultOptions: PlaywrightExportOptions = {
      language: 'javascript',
      includeComments: true,
      includeAssertions: true,
      includeWaitFor: true,
      filename: `playwright-test-${Date.now()}.js`,
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Generate script asynchronously and then export
    this.generateScript(finalOptions)
      .then(script => {
        // Use chrome.downloads API for extension context
        const blob = new Blob([script], { type: 'text/javascript' });
        const reader = new FileReader();

        reader.onload = () => {
          const dataUrl = reader.result as string;

          chrome.downloads.download(
            {
              url: dataUrl,
              filename: finalOptions.filename!,
              saveAs: true,
            },
            downloadId => {
              if (chrome.runtime.lastError) {
                logger.error('Download failed', chrome.runtime.lastError);
              } else {
                logger.info('Playwright script exported', { filename: finalOptions.filename, downloadId });
              }
            },
          );
        };

        reader.readAsDataURL(blob);
      })
      .catch(error => {
        logger.error('Failed to generate script for export', error);
      });
  }

  /**
   * Export actions to JSON format
   */
  static async exportActionsToJSON(): Promise<string> {
    return await actionTracker.exportToJSON();
  }

  /**
   * Import actions from JSON format
   */
  static async importActionsFromJSON(jsonData: string): Promise<void> {
    await actionTracker.importFromJSON(jsonData);
  }

  /**
   * Reset the action tracker
   */
  static async resetTracker(): Promise<void> {
    await actionTracker.reset();
  }

  /**
   * Get session information
   */
  static async getSessionInfo(): Promise<{ sessionId: string | null; actionCount: number }> {
    return await actionTracker.getSessionInfo();
  }

  /**
   * Get available languages for script generation
   */
  static getAvailableLanguages(): Array<{ value: string; label: string }> {
    return [
      { value: 'javascript', label: 'JavaScript' },
      { value: 'typescript', label: 'TypeScript' },
      { value: 'python', label: 'Python' },
      { value: 'java', label: 'Java' },
      { value: 'csharp', label: 'C#' },
    ];
  }

  /**
   * Re-run tracked actions directly in the browser without AI agents
   */
  static async reRunActions(): Promise<void> {
    const actions = await actionTracker.getTrackedActions();

    if (actions.length === 0) {
      throw new Error('No actions to re-run');
    }

    // Filter out non-executable actions
    const executableActions = actions.filter(action => {
      const executableActionTypes = [
        'go_to_url',
        'click_element',
        'input_text',
        'select_dropdown_option',
        'send_keys',
        'wait',
        'scroll_to_text',
        'scroll_to_percent',
        'scroll_to_top',
        'scroll_to_bottom',
        'search_google',
        'go_back',
      ];
      return executableActionTypes.includes(action.name);
    });

    if (executableActions.length === 0) {
      throw new Error('No executable actions found to re-run');
    }

    // Get the current active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length === 0) {
      throw new Error('No active tab found');
    }

    const activeTab = tabs[0];
    if (!activeTab.id) {
      throw new Error('Active tab has no ID');
    }

    // Execute actions in the browser context
    for (const action of executableActions) {
      try {
        await this.executeAction(action, activeTab.id);

        // Add a small delay between actions to ensure stability
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        logger.error('Failed to execute action', { action, error });
        throw new Error(`Failed to execute action ${action.name}: ${error}`);
      }
    }
  }

  /**
   * Execute a single action in the browser
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private static async executeAction(action: any, tabId: number): Promise<void> {
    const { name, parameters } = action;

    switch (name) {
      case 'go_to_url':
        await chrome.tabs.update(tabId, { url: parameters.url });
        break;

      case 'click_element':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: selector => {
            const element = document.querySelector(selector);
            if (element) {
              element.click();
            } else {
              throw new Error(`Element not found: ${selector}`);
            }
          },
          args: [action.selector || 'body'],
        });
        break;

      case 'input_text':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: (selector, text) => {
            const element = document.querySelector(selector) as HTMLInputElement;
            if (element) {
              element.value = text;
              element.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
              throw new Error(`Input element not found: ${selector}`);
            }
          },
          args: [action.selector || 'body', parameters.text],
        });
        break;

      case 'select_dropdown_option':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: (selector, text) => {
            const element = document.querySelector(selector) as HTMLSelectElement;
            if (element) {
              const option = Array.from(element.options).find(opt => opt.text === text);
              if (option) {
                element.value = option.value;
                element.dispatchEvent(new Event('change', { bubbles: true }));
              } else {
                throw new Error(`Option not found: ${text}`);
              }
            } else {
              throw new Error(`Select element not found: ${selector}`);
            }
          },
          args: [action.selector || 'body', parameters.text],
        });
        break;

      case 'send_keys':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: keys => {
            // Simulate keyboard input
            const event = new KeyboardEvent('keydown', { key: keys, bubbles: true });
            document.dispatchEvent(event);
          },
          args: [parameters.keys],
        });
        break;

      case 'wait':
        // Wait for specified seconds
        await new Promise(resolve => setTimeout(resolve, (parameters.seconds || 3) * 1000));
        break;

      case 'scroll_to_text':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: (text, nth) => {
            const elements = Array.from(document.querySelectorAll('*')).filter(el => el.textContent?.includes(text));
            if (elements[nth - 1]) {
              elements[nth - 1].scrollIntoView({ behavior: 'smooth' });
            } else {
              throw new Error(`Text not found: ${text}`);
            }
          },
          args: [parameters.text, parameters.nth || 1],
        });
        break;

      case 'scroll_to_percent':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: yPercent => {
            window.scrollTo(0, (document.body.scrollHeight * yPercent) / 100);
          },
          args: [parameters.yPercent],
        });
        break;

      case 'scroll_to_top':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            window.scrollTo(0, 0);
          },
          args: [],
        });
        break;

      case 'scroll_to_bottom':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            window.scrollTo(0, document.body.scrollHeight);
          },
          args: [],
        });
        break;

      case 'search_google': {
        const query = parameters.query;
        const encodedQuery = encodeURIComponent(query);
        const url = `https://www.google.com/search?q=${encodedQuery}`;
        await chrome.tabs.update(tabId, { url });
        break;
      }

      case 'go_back':
        await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            window.history.back();
          },
          args: [],
        });
        break;

      default:
        logger.info('Unsupported action for re-run', { action });
        break;
    }
  }

  /**
   * Generate a complete test file with setup instructions
   */
  static async generateTestFile(options: PlaywrightExportOptions = {}): Promise<{ script: string; setup: string }> {
    const script = await this.generateScript(options);
    const language = options.language || 'javascript';

    let setup = '';
    switch (language) {
      case 'javascript':
        setup = `// Setup: npm install playwright
// Run: node test.js`;
        break;
      case 'typescript':
        setup = `// Setup: npm install playwright @playwright/test
// Run: npx playwright test`;
        break;
      case 'python':
        setup = `# Setup: pip install playwright
# Run: python test.py`;
        break;
      case 'java':
        setup = `// Setup: Add to pom.xml:
// <dependency>
//   <groupId>com.microsoft.playwright</groupId>
//   <artifactId>playwright</artifactId>
//   <version>1.40.0</version>
// </dependency>
// Run: mvn compile exec:java -Dexec.mainClass="GeneratedTest"`;
        break;
      case 'csharp':
        setup = `// Setup: dotnet add package Microsoft.Playwright
// Run: dotnet run`;
        break;
    }

    return { script, setup };
  }
}

export default PlaywrightGenerator;
