// Action Tracker Service
import { logger } from '../log';
import { playwrightStorage } from '@extension/storage';

export interface TrackedAction {
  id: string;
  name: string;
  parameters: Record<string, unknown>;
  result: string | null;
  selector?: string;
  xpath?: string;
  timestamp: number;
  sessionId: string;
}

export interface PlaywrightScriptOptions {
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
  includeComments: boolean;
  includeAssertions: boolean;
  includeWaitFor: boolean;
}

class ActionTracker {
  private sessionId: string | null = null;

  constructor() {
    this.initializeSession();
  }

  private async initializeSession() {
    const sessionInfo = await playwrightStorage.getSessionInfo();
    this.sessionId = sessionInfo.sessionId;

    if (!this.sessionId) {
      this.sessionId = this.generateSessionId();
      await playwrightStorage.setSessionInfo({ sessionId: this.sessionId, actionCount: 0 });
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async trackAction(
    name: string,
    parameters: Record<string, unknown>,
    result: string | null = null,
    selector?: string,
    xpath?: string,
  ): Promise<void> {
    const action: TrackedAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      parameters,
      result,
      selector,
      xpath,
      timestamp: Date.now(),
      sessionId: this.sessionId!,
    };

    await playwrightStorage.addTrackedAction(action);

    // Update session info
    const actions = await playwrightStorage.getTrackedActions();
    await playwrightStorage.updateActionCount(actions.length);

    logger.info('Action tracked', { name, parameters, sessionId: this.sessionId });
  }

  async getTrackedActions(): Promise<TrackedAction[]> {
    return await playwrightStorage.getTrackedActions();
  }

  async getSessionInfo(): Promise<{ sessionId: string | null; actionCount: number }> {
    return await playwrightStorage.getSessionInfo();
  }

  async reset(): Promise<void> {
    this.sessionId = this.generateSessionId();
    await playwrightStorage.clearTrackedActions();
    await playwrightStorage.setSessionInfo({ sessionId: this.sessionId, actionCount: 0 });
    logger.info('Action tracker reset', { sessionId: this.sessionId });
  }

  async exportToJSON(): Promise<string> {
    return await playwrightStorage.exportToJSON();
  }

  async importFromJSON(jsonData: string): Promise<void> {
    await playwrightStorage.importFromJSON(jsonData);
    const sessionInfo = await playwrightStorage.getSessionInfo();
    this.sessionId = sessionInfo.sessionId;
    logger.info('Actions imported from JSON', { sessionId: this.sessionId });
  }

  async generatePlaywrightScript(options: Partial<PlaywrightScriptOptions> = {}): Promise<string> {
    const actions = await this.getTrackedActions();

    if (actions.length === 0) {
      return '// No actions tracked yet. Start performing actions in the browser to generate a Playwright script.';
    }

    const defaultOptions: PlaywrightScriptOptions = {
      language: 'javascript',
      includeComments: true,
      includeAssertions: true,
      includeWaitFor: true,
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Save the generated script to storage
    const script = this.convertActionsToPlaywright(actions, finalOptions);
    await playwrightStorage.saveGeneratedScript(this.sessionId!, script);

    return script;
  }

  private convertActionsToPlaywright(actions: TrackedAction[], options: PlaywrightScriptOptions): string {
    const { language, includeComments, includeAssertions, includeWaitFor } = options;

    let script = this.getLanguageHeader(language);
    script += this.getLanguageSetup(language);

    if (includeComments) {
      script += `\n// Generated Playwright script from ${actions.length} tracked actions\n`;
      script += `// Session ID: ${this.sessionId}\n`;
      script += `// Generated at: ${new Date().toLocaleString()}\n\n`;
    }

    script += 'async function runTest() {\n';
    script += "  const { chromium } = require('playwright');\n\n";
    script += '  const browser = await chromium.launch({ headless: false });\n';
    script += '  const context = await browser.newContext();\n';
    script += '  const page = await context.newPage();\n\n';

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];

      if (includeComments) {
        script += `  // Action ${i + 1}: ${action.name}\n`;
      }

      script += this.convertActionToPlaywright(action, includeAssertions);

      if (includeWaitFor && i < actions.length - 1) {
        script += this.getLanguageWait(language);
      }
    }

    script += '\n  await browser.close();\n';
    script += '}\n\n';
    script += 'runTest().catch(console.error);\n';
    script += this.getLanguageFooter(language);

    return script;
  }

  private convertActionToPlaywright(action: TrackedAction, includeAssertions: boolean): string {
    switch (action.name) {
      case 'go_to_url':
        return this.generateGoToUrl(action, includeAssertions);
      case 'search_google':
        return this.generateSearchGoogle(action, includeAssertions);
      case 'go_back':
        return this.generateGoBack(action, includeAssertions);
      case 'click_element':
        return this.generateClickElement(action, includeAssertions);
      case 'input_text':
        return this.generateInputText(action, includeAssertions);
      case 'select_option':
        return this.generateSelectOption(action, includeAssertions);
      case 'select_dropdown_option':
        return this.generateSelectDropdownOption(action, includeAssertions);
      case 'press_key':
        return this.generatePressKey(action);
      case 'send_keys':
        return this.generateSendKeys(action);
      case 'wait_for_timeout':
        return this.generateWaitForTimeout(action);
      case 'wait':
        return this.generateWait(action);
      case 'evaluate_script':
        return this.generateEvaluateScript(action);
      case 'scroll_to_percent':
        return this.generateScrollToPercent(action);
      case 'scroll_to_top':
        return this.generateScrollToTop(action);
      case 'scroll_to_bottom':
        return this.generateScrollToBottom(action);
      case 'scroll_to_text':
        return this.generateScrollToText(action);
      default:
        return `  // Unsupported action: ${action.name}\n`;
    }
  }

  private generateGoToUrl(action: TrackedAction, includeAssertions: boolean): string {
    const url = action.parameters.url as string;
    let script = `  await page.goto('${url}');\n`;
    script += `  await page.waitForLoadState('networkidle');\n`;

    if (includeAssertions) {
      script += `  expect(page).toHaveURL('${url}');\n`;
    }

    return script;
  }

  private generateSearchGoogle(action: TrackedAction, includeAssertions: boolean): string {
    const query = action.parameters.query as string;
    const encodedQuery = encodeURIComponent(query);
    const url = `https://www.google.com/search?q=${encodedQuery}`;

    let script = `  await page.goto('${url}');\n`;
    script += `  await page.waitForLoadState('networkidle');\n`;

    if (includeAssertions) {
      script += `  expect(page).toHaveURL('${url}');\n`;
    }

    return script;
  }

  private generateGoBack(action: TrackedAction, includeAssertions: boolean): string {
    let script = `  await page.goBack();\n`;
    script += `  await page.waitForLoadState('networkidle');\n`;

    if (includeAssertions) {
      script += `  expect(page).not.toHaveURL('');\n`;
    }

    return script;
  }

  private generateClickElement(action: TrackedAction, includeAssertions: boolean): string {
    const selector = this.getSelector(action);
    let script = `  await page.locator('${selector}').click();\n`;
    script += `  await page.waitForLoadState('networkidle');\n`;

    if (includeAssertions) {
      script += `  expect(page.locator('${selector}')).toBeVisible();\n`;
    }

    return script;
  }

  private generateInputText(action: TrackedAction, includeAssertions: boolean): string {
    const selector = this.getSelector(action);
    const text = action.parameters.text as string;
    let script = `  await page.locator('${selector}').fill('${text}');\n`;

    if (includeAssertions) {
      script += `  expect(page.locator('${selector}')).toHaveValue('${text}');\n`;
    }

    return script;
  }

  private generateSelectOption(action: TrackedAction, includeAssertions: boolean): string {
    const selector = this.getSelector(action);
    const value = action.parameters.value as string;
    let script = `  await page.locator('${selector}').selectOption('${value}');\n`;

    if (includeAssertions) {
      script += `  expect(page.locator('${selector}')).toHaveValue('${value}');\n`;
    }

    return script;
  }

  private generateSelectDropdownOption(action: TrackedAction, includeAssertions: boolean): string {
    const selector = this.getSelector(action);
    const text = action.parameters.text as string;
    let script = `  await page.locator('${selector}').selectOption({ label: '${text}' });\n`;

    if (includeAssertions) {
      script += `  expect(page.locator('${selector}')).toHaveValue('${text}');\n`;
    }

    return script;
  }

  private generatePressKey(action: TrackedAction): string {
    const key = action.parameters.key as string;
    return `  await page.keyboard.press('${key}');\n`;
  }

  private generateSendKeys(action: TrackedAction): string {
    const keys = action.parameters.keys as string;
    return `  await page.keyboard.press('${keys}');\n`;
  }

  private generateWaitForTimeout(action: TrackedAction): string {
    const timeout = (action.parameters.timeout as number) || 1000;
    return `  await page.waitForTimeout(${timeout});\n`;
  }

  private generateWait(action: TrackedAction): string {
    const seconds = (action.parameters.seconds as number) || 3;
    return `  await page.waitForTimeout(${seconds * 1000});\n`;
  }

  private generateEvaluateScript(action: TrackedAction): string {
    const script = action.parameters.script as string;
    return `  await page.evaluate(\`${script}\`);\n`;
  }

  private generateScrollToPercent(action: TrackedAction): string {
    const yPercent = action.parameters.yPercent as number;
    const index = action.parameters.index as number | null;

    if (index !== null) {
      const selector = this.getSelector(action);
      return `  await page.locator('${selector}').scrollIntoViewIfNeeded();\n  await page.evaluate((yPercent) => window.scrollTo(0, (document.body.scrollHeight * yPercent) / 100), ${yPercent});\n`;
    } else {
      return `  await page.evaluate((yPercent) => window.scrollTo(0, (document.body.scrollHeight * yPercent) / 100), ${yPercent});\n`;
    }
  }

  private generateScrollToTop(action: TrackedAction): string {
    const index = action.parameters.index as number | null;

    if (index !== null) {
      const selector = this.getSelector(action);
      return `  await page.locator('${selector}').scrollIntoViewIfNeeded();\n  await page.evaluate(() => window.scrollTo(0, 0));\n`;
    } else {
      return `  await page.evaluate(() => window.scrollTo(0, 0));\n`;
    }
  }

  private generateScrollToBottom(action: TrackedAction): string {
    const index = action.parameters.index as number | null;

    if (index !== null) {
      const selector = this.getSelector(action);
      return `  await page.locator('${selector}').scrollIntoViewIfNeeded();\n  await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));\n`;
    } else {
      return `  await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));\n`;
    }
  }

  private generateScrollToText(action: TrackedAction): string {
    const text = action.parameters.text as string;
    const nth = (action.parameters.nth as number) || 1;
    return `  await page.locator('text=${text}').nth(${nth - 1}).scrollIntoViewIfNeeded();\n`;
  }

  private getSelector(action: TrackedAction): string {
    // Prefer data-testid, then CSS selector, then XPath
    if (action.selector?.includes('data-testid=')) {
      return action.selector;
    }
    if (action.selector) {
      return action.selector;
    }
    if (action.xpath) {
      return action.xpath;
    }
    return 'body'; // Fallback
  }

  private getLanguageHeader(language: string): string {
    switch (language) {
      case 'typescript':
        return "import { test, expect } from '@playwright/test';\n\n";
      case 'python':
        return 'from playwright.sync_api import sync_playwright\n\n';
      case 'java':
        return 'import com.microsoft.playwright.*;\n\n';
      case 'csharp':
        return 'using Microsoft.Playwright;\n\n';
      default:
        return "const { chromium } = require('playwright');\n\n";
    }
  }

  private getLanguageSetup(language: string): string {
    switch (language) {
      case 'typescript':
        return "test('Generated Test', async ({ page }) => {\n";
      case 'python':
        return 'def run_test():\n';
      case 'java':
        return 'public class GeneratedTest {\n  public static void main(String[] args) {\n';
      case 'csharp':
        return 'class GeneratedTest {\n  public static async Task Main(string[] args) {\n';
      default:
        return '';
    }
  }

  private getLanguageWait(language: string): string {
    switch (language) {
      case 'typescript':
        return '  await page.waitForTimeout(1000);\n';
      case 'python':
        return '    page.wait_for_timeout(1000)\n';
      case 'java':
        return '    page.waitForTimeout(1000);\n';
      case 'csharp':
        return '    await page.WaitForTimeoutAsync(1000);\n';
      default:
        return '  await page.waitForTimeout(1000);\n';
    }
  }

  private getLanguageFooter(language: string): string {
    switch (language) {
      case 'typescript':
        return '});\n';
      case 'python':
        return '\nif __name__ == "__main__":\n  run_test()\n';
      case 'java':
        return '  }\n}\n';
      case 'csharp':
        return '  }\n}\n';
      default:
        return '';
    }
  }
}

export const actionTracker = new ActionTracker();
