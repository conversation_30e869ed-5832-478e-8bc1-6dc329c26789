# Core settings
* text=auto eol=lf

# TypeScript/React files
*.ts linguist-language=TypeScript
*.tsx linguist-language=TypeScript

# JavaScript files
*.js linguist-language=JavaScript
*.cjs linguist-language=JavaScript
*.mjs linguist-language=JavaScript

# Web files
*.html text
*.css text
*.json text

# Scripts
*.sh text eol=lf

# Documentation
*.md text

# Config files (used in project)
.eslintrc text
.prettierrc text
.nvmrc text
tsconfig.json text
turbo.json text

# Chrome extension
*.crx binary
*.pem binary

# Fonts (used in UI)
*.woff2 binary
*.ttf binary

# Images (used in UI)
*.svg text
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary

# Additional common file types
*.yaml text
*.yml text
*.lock text
package-lock.json text -diff
yarn.lock text -diff
pnpm-lock.yaml text -diff