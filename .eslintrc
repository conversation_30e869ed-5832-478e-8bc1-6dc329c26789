{
  "env": {
    "browser": true,
    "es6": true,
    "node": true,
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react-hooks/recommended",
    "plugin:import/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:tailwindcss/recommended",
    "prettier",
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true,
    },
    "ecmaVersion": "latest",
    "sourceType": "module",
  },
  "plugins": ["react", "@typescript-eslint", "react-hooks", "import", "jsx-a11y", "prettier"],
  "settings": {
    "react": {
      "version": "detect",
    },
  },
  "rules": {
    "react/react-in-jsx-scope": "off",
    "import/no-unresolved": "off",
    "@typescript-eslint/consistent-type-imports": "error",
  },
  "globals": {
    "chrome": "readonly",
  },
  "ignorePatterns": ["watch.js", "dist/**"],
}
